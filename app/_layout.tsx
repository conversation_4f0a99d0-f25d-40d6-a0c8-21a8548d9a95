import AsyncStorage from '@react-native-async-storage/async-storage';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Redirect, Stack, useRouter, useSegments } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, View } from 'react-native';
import 'react-native-reanimated';

import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { AppProvider, useAppContext } from '@/context/AppContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import '@/utils/i18n'; // Import for side effects (i18n initialization)
import { requestStoragePermissions } from '@/utils/storageUtils';

// Keep the splash screen visible until we're ready to render
SplashScreen.preventAutoHideAsync();

function RootLayoutNav() {
  const colorScheme = useColorScheme();
  const { isDarkMode, isLoading: isContextLoading, error } = useAppContext();
  const router = useRouter();
  const segments = useSegments();
  const [isSetupCompleted, setIsSetupCompleted] = useState<boolean | null>(null);
  const [hasPermissions, setHasPermissions] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { i18n, t } = useTranslation();

  useEffect(() => {
    const checkSetupStatus = async () => {
      try {
        setIsLoading(true);
        const hasCompletedSetup = await AsyncStorage.getItem('hasCompletedSetup');
        const language = await AsyncStorage.getItem('language');
        
        if (language) {
          i18n.changeLanguage(language);
        }
        
        setIsSetupCompleted(hasCompletedSetup === 'true');
        
        // Request storage permissions if setup is completed
        if (hasCompletedSetup === 'true') {
          const permissionsGranted = await requestStoragePermissions();
          setHasPermissions(permissionsGranted);
          
          if (!permissionsGranted) {
            Alert.alert(
              'Permissions Required',
              'This app needs access to your media library to analyze and optimize your storage. Without these permissions, some features may not work correctly.',
              [
                { 
                  text: 'OK',
                  onPress: () => console.log('Permission denied by user')
                }
              ]
            );
          }
        }
        
        await SplashScreen.hideAsync();
      } catch (error) {
        console.error('Error checking setup status:', error);
        setIsSetupCompleted(false);
        setHasPermissions(false);
        await SplashScreen.hideAsync();
      } finally {
        setIsLoading(false);
      }
    };

    checkSetupStatus();
  }, []);

  // Show a loading state while we check if setup is completed
  if (isLoading || isContextLoading || isSetupCompleted === null) {
    return null;
  }

  // If setup is not completed and we're not already on the language selection screen,
  // redirect to the language selection screen
  if (!isSetupCompleted && segments[0] !== 'language-selection') {
    return <Redirect href="/language-selection" />;
  }

  return (
    <ThemeProvider value={isDarkMode ? DarkTheme : DefaultTheme}>
      {error && (
        <View style={{ position: 'absolute', top: 50, left: 16, right: 16, zIndex: 1000 }}>
          <ErrorMessage />
        </View>
      )}
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="language-selection" options={{ gestureEnabled: false }} />
        <Stack.Screen name="(tabs)" options={{ gestureEnabled: false }} />
      </Stack>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
    </ThemeProvider>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <AppProvider>
      <RootLayoutNav />
    </AppProvider>
  );
}
