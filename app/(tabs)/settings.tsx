import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, Linking, SafeAreaView, ScrollView, StyleSheet, Switch, Text, TouchableOpacity, View } from 'react-native';

import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import { requestStoragePermissions } from '@/utils/storageUtils';

export default function SettingsTab() {
  const { t } = useTranslation();
  const { isDarkMode, toggleDarkMode, isAIEnabled, toggleAIEnabled, language } = useAppContext();
  const [protectSystemFiles, setProtectSystemFiles] = useState(true);
  const [confirmBeforeDelete, setConfirmBeforeDelete] = useState(true);
  const [advancedModeEnabled, setAdvancedModeEnabled] = useState(false);
  const [hasStoragePermissions, setHasStoragePermissions] = useState<boolean | null>(null);

  // New enhanced settings
  const [autoCleanEnabled, setAutoCleanEnabled] = useState(false);
  const [smartOrganizationEnabled, setSmartOrganizationEnabled] = useState(false);
  const [compressionQuality, setCompressionQuality] = useState(0.7);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [autoBackupEnabled, setAutoBackupEnabled] = useState(false);
  const [showOrganizationModal, setShowOrganizationModal] = useState(false);
  const [fileOrganizer] = useState(new SmartFileOrganizer());

  // Get color scheme based on dark mode
  const colorScheme = isDarkMode ? 'dark' : 'light';
  const backgroundColor = Colors[colorScheme].background;
  const textColor = Colors[colorScheme].text;
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';

  // Check if we have storage permissions
  useEffect(() => {
    const checkPermissions = async () => {
      const permissions = await requestStoragePermissions();
      setHasStoragePermissions(permissions);
    };

    checkPermissions();
  }, []);

  const toggleProtectSystemFiles = () => {
    if (protectSystemFiles && !advancedModeEnabled) {
      Alert.alert(
        t('settings.securityWarning'),
        t('settings.securityWarningMessage'),
        [
          { text: t('common.cancel'), style: "cancel" },
          {
            text: t('settings.proceed'),
            style: "destructive",
            onPress: () => setProtectSystemFiles(false)
          }
        ]
      );
    } else {
      setProtectSystemFiles(!protectSystemFiles);
    }
  };

  const requestPermissions = async () => {
    const granted = await requestStoragePermissions();
    setHasStoragePermissions(granted);

    if (!granted) {
      Alert.alert(
        t('settings.permissionsRequired'),
        t('settings.permissionsRequiredMessage'),
        [
          { text: t('common.cancel'), style: "cancel" },
          {
            text: t('settings.openSettings'),
            onPress: () => Linking.openSettings()
          }
        ]
      );
    } else {
      Alert.alert(
        t('settings.permissionsGranted'),
        t('settings.permissionsGrantedMessage')
      );
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: textColor }]}>{t('settings.title')}</Text>
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        {/* Appearance Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>{t('settings.display')}</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>{t('settings.darkMode')}</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                {t('settings.darkModeDescription')}
              </Text>
            </View>
            <Switch
              value={isDarkMode}
              onValueChange={toggleDarkMode}
              trackColor={{ false: '#767577', true: Colors[colorScheme].tint }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        {/* Permissions Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>{t('settings.permissions')}</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>{t('settings.storageAccess')}</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                {t('settings.storageAccessDescription')}
              </Text>
            </View>
            {hasStoragePermissions !== null && (
              <View style={styles.permissionStatus}>
                <Text style={[
                  styles.permissionStatusText,
                  {
                    color: hasStoragePermissions ? '#34C759' : '#FF3B30',
                    marginRight: 8
                  }
                ]}>
                  {hasStoragePermissions ? t('settings.granted') : t('settings.denied')}
                </Text>
                {!hasStoragePermissions && (
                  <TouchableOpacity
                    style={[styles.permissionButton, { backgroundColor: '#0A7EA4' }]}
                    onPress={requestPermissions}
                  >
                    <Text style={styles.permissionButtonText}>{t('settings.request')}</Text>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>
        </View>

        {/* Security Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>{t('settings.securityProtection')}</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>{t('settings.protectSystemFiles')}</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                {t('settings.protectSystemFilesDescription')}
              </Text>
            </View>
            <Switch
              value={protectSystemFiles}
              onValueChange={toggleProtectSystemFiles}
              trackColor={{ false: '#767577', true: '#34C759' }}
              thumbColor="#FFFFFF"
            />
          </View>

          <View style={styles.divider} />

          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>{t('settings.confirmDeletions')}</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                {t('settings.confirmDeletionsDescription')}
              </Text>
            </View>
            <Switch
              value={confirmBeforeDelete}
              onValueChange={setConfirmBeforeDelete}
              trackColor={{ false: '#767577', true: '#34C759' }}
              thumbColor="#FFFFFF"
            />
          </View>

          <View style={styles.divider} />

          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>{t('settings.advancedMode')}</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                {t('settings.advancedModeDescription')}
              </Text>
            </View>
            <Switch
              value={advancedModeEnabled}
              onValueChange={setAdvancedModeEnabled}
              trackColor={{ false: '#767577', true: '#FF9500' }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        {/* Language Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>{t('settings.languageRegion')}</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <TouchableOpacity
            style={styles.settingRow}
            onPress={() => router.push("/language-selection")}
          >
            <Text style={[styles.settingText, { color: textColor }]}>{t('settings.appLanguage')}</Text>
            <View style={styles.settingAction}>
              <Text style={[styles.languageText, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                {language === 'en' ? 'English' : 'हिन्दी'}
              </Text>
              <IconSymbol name="chevron.right" size={20} color={isDarkMode ? '#9BA1A6' : '#687076'} />
            </View>
          </TouchableOpacity>
        </View>

        {/* Features Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>{t('settings.smartFeatures')}</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <View style={styles.settingRow}>
            <View style={styles.settingLabelContainer}>
              <Text style={[styles.settingText, { color: textColor }]}>{t('settings.aiAnalysis')}</Text>
              <Text style={[styles.settingDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                {t('settings.aiAnalysisDescription')}
              </Text>
            </View>
            <Switch
              value={isAIEnabled}
              onValueChange={toggleAIEnabled}
              trackColor={{ false: '#767577', true: Colors[colorScheme].tint }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        {/* About Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>{t('settings.about')}</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <TouchableOpacity style={styles.settingRow}>
            <Text style={[styles.settingText, { color: textColor }]}>{t('settings.appVersion')}</Text>
            <Text style={[styles.versionText, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>1.0.0</Text>
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity style={styles.settingRow}>
            <Text style={[styles.settingText, { color: textColor }]}>{t('settings.privacyPolicy')}</Text>
            <IconSymbol name="chevron.right" size={20} color={isDarkMode ? '#9BA1A6' : '#687076'} />
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity style={styles.settingRow}>
            <Text style={[styles.settingText, { color: textColor }]}>{t('settings.termsOfService')}</Text>
            <IconSymbol name="chevron.right" size={20} color={isDarkMode ? '#9BA1A6' : '#687076'} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    paddingTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  content: {
    padding: 16,
    paddingBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 16,
  },
  card: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  settingLabelContainer: {
    flex: 1,
    marginRight: 8,
  },
  settingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: 12,
    marginTop: 4,
  },
  settingAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageText: {
    marginRight: 8,
    fontSize: 14,
  },
  versionText: {
    fontSize: 14,
  },
  divider: {
    height: 1,
    backgroundColor: '#333333',
    opacity: 0.2,
  },
  permissionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permissionStatusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  permissionButton: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 4,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
});