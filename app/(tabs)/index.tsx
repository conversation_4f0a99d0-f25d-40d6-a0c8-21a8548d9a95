import { ProgressBar } from '@/components/ui/ProgressBar';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RefreshControl, SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { ErrorMessage } from '@/components/ui/ErrorMessage';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingOverlay } from '@/components/ui/LoadingOverlay';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import { executeWithErrorHandling } from '@/utils/errorHandling';
import { formatBytes, getMediaFiles, getStorageInfo, requestStoragePermissions } from '@/utils/storageUtils';

interface StorageData {
  totalSpace: number;
  freeSpace: number;
  usedSpace: number;
  usedPercentage: number;
  mediaUsage?: {
    images: number;
    videos: number;
    other: number;
  };
}

export default function HomeScreen() {
  const { t } = useTranslation();
  const { isDarkMode, isAIEnabled } = useAppContext();
  const router = useRouter();
  const [storageData, setStorageData] = useState<StorageData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [hasPermissions, setHasPermissions] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Get color scheme based on dark mode
  const colorScheme = isDarkMode ? 'dark' : 'light';
  const backgroundColor = Colors[colorScheme].background;
  const textColor = Colors[colorScheme].text;
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';
  const accentColor = Colors[colorScheme].tint;

  // Check permissions
  useEffect(() => {
    const checkPermissions = async () => {
      const permissions = await requestStoragePermissions();
      setHasPermissions(permissions);

      if (permissions) {
        fetchStorageData();
      }
    };

    checkPermissions();
  }, []);

  // Fetch storage information
  const fetchStorageData = async () => {
    setIsLoading(true);
    setError(null);

    const result = await executeWithErrorHandling(async () => {
      const info = await getStorageInfo();
      const usedPercentage = (info.usedSpace / info.totalSpace) * 100;

      // Get media storage breakdown
      let mediaUsage = {
        images: 0,
        videos: 0,
        other: 0
      };

      try {
        const mediaFiles = await getMediaFiles();

        // Calculate size by type
        mediaFiles.forEach(file => {
          if (file.type === 'image') {
            mediaUsage.images += file.size;
          } else if (file.type === 'video') {
            mediaUsage.videos += file.size;
          } else {
            mediaUsage.other += file.size;
          }
        });
      } catch (error) {
        console.error('Error getting media breakdown:', error);
      }

      return {
        ...info,
        usedPercentage,
        mediaUsage
      };
    }, 'Failed to fetch storage data', false);

    if (result) {
      setStorageData(result);
    } else {
      setError('Could not retrieve storage information. Please try again.');
    }

    setIsLoading(false);
    setRefreshing(false);
  };

  // Handle pull-to-refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchStorageData();
  };

  // Function to render a quick action button
  const QuickActionButton = ({ title, icon, destination }: { title: string; icon: string; destination: any }) => (
    <TouchableOpacity
      style={[styles.actionButton, { backgroundColor: cardColor }]}
      onPress={() => router.push(destination)}
    >
      <IconSymbol name={icon} size={32} color={accentColor} />
      <Text style={[styles.actionButtonText, { color: textColor }]}>{title}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {error && <ErrorMessage message={error} onDismiss={() => setError(null)} />}

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={accentColor}
            colors={[accentColor]}
          />
        }
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: textColor }]}>{t('home.title')}</Text>
          {!hasPermissions && (
            <TouchableOpacity
              style={[styles.permissionButton, { backgroundColor: accentColor }]}
              onPress={async () => {
                const granted = await requestStoragePermissions();
                setHasPermissions(granted);
                if (granted) {
                  fetchStorageData();
                }
              }}
            >
              <Text style={styles.permissionButtonText}>{t('common.grantPermissions')}</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Storage Usage Card */}
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <Text style={[styles.cardTitle, { color: textColor }]}>{t('home.storageUsage')}</Text>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <LoadingOverlay
                message={t('home.loadingStorageInfo')}
                isDarkMode={isDarkMode}
                textColor={textColor}
              />
            </View>
          ) : !hasPermissions ? (
            <View style={styles.permissionContainer}>
              <Text style={[styles.permissionText, { color: textColor }]}>
                {t('home.storagePermissionsRequired')}
              </Text>
              <TouchableOpacity
                style={[styles.permissionButton, { backgroundColor: accentColor }]}
                onPress={async () => {
                  const granted = await requestStoragePermissions();
                  setHasPermissions(granted);
                  if (granted) {
                    fetchStorageData();
                  }
                }}
              >
                <Text style={styles.permissionButtonText}>{t('common.grantPermissions')}</Text>
              </TouchableOpacity>
            </View>
          ) : storageData ? (
            <>
              <View style={styles.storageInfo}>
                <Text style={[styles.percentageText, { color: textColor }]}>
                  {storageData.usedPercentage.toFixed(1)}%
                </Text>
                <Text style={[styles.storageText, { color: textColor }]}>
                  {formatBytes(storageData.usedSpace)} / {formatBytes(storageData.totalSpace)}
                </Text>
              </View>

              <ProgressBar
                progress={storageData.usedPercentage / 100}
                style={styles.progressBar}
                progressColor={storageData.usedPercentage > 90 ? '#FF3B30' : '#34C759'}
                trackColor={isDarkMode ? '#333' : '#E5E5EA'}
              />

              <View style={styles.storageDetails}>
                <View style={styles.storageDetailItem}>
                  <View style={[styles.colorIndicator, { backgroundColor: '#34C759' }]} />
                  <Text style={[styles.detailText, { color: textColor }]}>
                    {t('common.free')}: {formatBytes(storageData.freeSpace)}
                  </Text>
                </View>

                <View style={styles.storageDetailItem}>
                  <View style={[styles.colorIndicator, { backgroundColor: '#FF9500' }]} />
                  <Text style={[styles.detailText, { color: textColor }]}>
                    {t('common.used')}: {formatBytes(storageData.usedSpace)}
                  </Text>
                </View>
              </View>

              {storageData.mediaUsage && (
                <View style={styles.mediaBreakdown}>
                  <Text style={[styles.mediaBreakdownTitle, { color: textColor }]}>
                    {t('home.mediaBreakdown')}
                  </Text>

                  <View style={styles.mediaBreakdownItem}>
                    <IconSymbol name="photo.fill" size={16} color={accentColor} />
                    <Text style={[styles.mediaBreakdownText, { color: textColor }]}>
                      {t('home.photos')}: {formatBytes(storageData.mediaUsage.images)}
                    </Text>
                  </View>

                  <View style={styles.mediaBreakdownItem}>
                    <IconSymbol name="video.fill" size={16} color={accentColor} />
                    <Text style={[styles.mediaBreakdownText, { color: textColor }]}>
                      {t('home.videos')}: {formatBytes(storageData.mediaUsage.videos)}
                    </Text>
                  </View>

                  <View style={styles.mediaBreakdownItem}>
                    <IconSymbol name="doc.fill" size={16} color={accentColor} />
                    <Text style={[styles.mediaBreakdownText, { color: textColor }]}>
                      {t('home.other')}: {formatBytes(storageData.mediaUsage.other)}
                    </Text>
                  </View>
                </View>
              )}
            </>
          ) : (
            <Text style={[styles.errorText, { color: textColor }]}>
              {t('home.couldNotLoadStorage')}
            </Text>
          )}
        </View>

        {/* Quick Actions Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>{t('home.quickActions')}</Text>
        <View style={styles.actionsContainer}>
          <QuickActionButton
            title={t('home.analyzeStorage')}
            icon="chart.pie.fill"
            destination="/(tabs)/analyze"
          />
          <QuickActionButton
            title={t('home.cleanStorage')}
            icon="trash.fill"
            destination="/(tabs)/clean"
          />
          <QuickActionButton
            title={t('home.compressFiles')}
            icon="arrow.down.to.line"
            destination="/(tabs)/compress"
          />
          {isAIEnabled && (
            <QuickActionButton
              title={t('home.smartAnalysis')}
              icon="wand.and.stars"
              destination="/(tabs)/analyze"
            />
          )}
        </View>

        {/* Tips Section */}
        <Text style={[styles.sectionTitle, { color: textColor }]}>{t('home.storageTips')}</Text>
        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <Text style={[styles.tipTitle, { color: textColor }]}>{t('home.freeUpSpaceQuickly')}</Text>
          <Text style={[styles.tipText, { color: textColor }]}>
            {t('home.freeUpSpaceTip')}
          </Text>
        </View>

        <View style={[styles.card, { backgroundColor: cardColor }]}>
          <Text style={[styles.tipTitle, { color: textColor }]}>{t('home.compressPhotos')}</Text>
          <Text style={[styles.tipText, { color: textColor }]}>
            {t('home.compressPhotosTip')}
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  card: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  storageInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  percentageText: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  storageText: {
    fontSize: 16,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 16,
  },
  storageDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  storageDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  detailText: {
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  actionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  actionButton: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  actionButtonText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
  },
  mediaBreakdown: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#333333',
    opacity: 0.2,
  },
  mediaBreakdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  mediaBreakdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  mediaBreakdownText: {
    marginLeft: 8,
    fontSize: 14,
  },
  permissionContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  permissionText: {
    textAlign: 'center',
    marginBottom: 16,
    fontSize: 14,
  },
  permissionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  errorText: {
    textAlign: 'center',
    marginBottom: 16,
    fontSize: 14,
  },
});
