import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert, FlatList, SafeAreaView, StyleSheet, Switch, Text, TouchableOpacity, View } from 'react-native';

import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import { FileInfo, cleanFiles, formatBytes, getCleanableFiles } from '@/utils/storageUtils';

type CleanCategory = 'junk' | 'cache' | 'appData' | 'downloads';

interface CleanItem extends FileInfo {
  description: string;
  category: CleanCategory;
}

export default function CleanScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useAppContext();
  
  const [activeCategory, setActiveCategory] = useState<CleanCategory>('junk');
  const [isLoading, setIsLoading] = useState(false);
  const [isCleaning, setIsCleaning] = useState(false);
  const [items, setItems] = useState<CleanItem[]>([]);
  const [scanCompleted, setScanCompleted] = useState(false);
  const [selectedItems, setSelectedItems] = useState<{[key: string]: boolean}>({});
  const [noItemsFound, setNoItemsFound] = useState(false);
  
  // Get color scheme based on dark mode
  const colorScheme = isDarkMode ? 'dark' : 'light';
  const backgroundColor = Colors[colorScheme].background;
  const textColor = Colors[colorScheme].text;
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';
  const accentColor = Colors[colorScheme].tint;

  // Function to scan for cleanable items based on the selected category
  const scanForCleanableItems = async () => {
    setIsLoading(true);
    setItems([]);
    setSelectedItems({});
    
    try {
      const cleanableFiles = await getCleanableFiles(activeCategory);
      
      if (cleanableFiles.length === 0) {
        // No items found for this category
        setIsLoading(false);
        setNoItemsFound(true);
        return;
      }
      
      // Reset flag in case previously set
      setNoItemsFound(false);
      
      // Generate descriptive items for the UI
      const cleanItems: CleanItem[] = cleanableFiles.map(file => {
        const description = getFileDescription(file, activeCategory);
        
        return {
          ...file,
          description,
          category: activeCategory,
          selected: false,
        };
      });
      
      // Sort by size (largest first)
      cleanItems.sort((a, b) => b.size - a.size);
      
      setItems(cleanItems);
      
      // Initialize selected items map
      const newSelectedItems: Record<string, boolean> = {};
      cleanItems.forEach(item => {
        const itemId = item.id || item.uri;
        newSelectedItems[itemId] = false;
      });
      setSelectedItems(newSelectedItems);
    } catch (error) {
      console.error('Error scanning for cleanable items:', error);
      Alert.alert(
        'Scan Error',
        'There was an error scanning for cleanable items. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };
  
  // Helper function to generate descriptive text for different file types
  const getFileDescription = (file: FileInfo, category: CleanCategory): string => {
    // Format date if available
    const formatDate = (timestamp?: number) => {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleDateString();
    };
    
    const modifiedDate = formatDate(file.modificationTime);
    const dateInfo = modifiedDate ? ` (${modifiedDate})` : '';
    
    switch (category) {
      case 'junk':
        if (file.name.includes('log') || file.name.endsWith('.log')) {
          return `Log file${dateInfo}`;
        } else if (file.name.includes('temp') || file.name.includes('tmp')) {
          return `Temporary file${dateInfo}`;
        } else if (file.name.includes('cache') || file.name.endsWith('.cache')) {
          return `Cache file${dateInfo}`;
        } else if (file.name.includes('backup') || file.name.endsWith('.bak')) {
          return `Backup file${dateInfo}`;
        }
        return `Unnecessary file${dateInfo}`;
        
      case 'cache':
        if (file.path.includes('/image_cache/')) {
          return `Cached image${dateInfo}`;
        } else if (file.path.includes('/video_cache/')) {
          return `Cached video${dateInfo}`;
        } else if (file.path.includes('/http_cache/')) {
          return `Web cache${dateInfo}`;
        }
        return `App cache file${dateInfo}`;
        
      case 'appData':
        if (file.type === 'document') {
          return `App document${dateInfo}`;
        } else if (file.name.includes('preferences') || file.name.includes('settings')) {
          return `App preferences${dateInfo}`;
        } else if (file.name.includes('data') || file.name.includes('db')) {
          return `App data${dateInfo}`;
        }
        return `App data file${dateInfo}`;
        
      case 'downloads':
        if (file.type === 'image') {
          return `Downloaded image${dateInfo}`;
        } else if (file.type === 'video') {
          return `Downloaded video${dateInfo}`;
        } else if (file.type === 'document') {
          return `Downloaded document${dateInfo}`;
        } else if (file.type === 'archive') {
          return `Downloaded archive${dateInfo}`;
        } else if (file.type === 'audio') {
          return `Downloaded audio${dateInfo}`;
        }
        return `Downloaded file${dateInfo}`;
        
      default:
        return `File${dateInfo}`;
    }
  };
  
  // Toggle item selection - improved version with separate state
  const toggleItemSelection = (id: string) => {
    try {
      // Update the selectedItems state
      setSelectedItems(prev => {
        const newSelectedItems = { ...prev };
        newSelectedItems[id] = !prev[id];
        return newSelectedItems;
      });
      
      // Also update the items array for consistency
      setItems(prevItems => 
        prevItems.map(item => {
          if (item.id === id || item.uri === id) {
            return { ...item, selected: !item.selected };
          }
          return item;
        })
      );
    } catch (error) {
      console.error('Error toggling item selection:', error);
      Alert.alert(
        'Selection Error',
        'Unable to select this item. It may be a protected system file.'
      );
    }
  };
  
  // Toggle all items selection
  const toggleSelectAll = () => {
    const areAllSelected = items.every((item) => selectedItems[item.id || item.uri]);
    
    const newSelectedItems = { ...selectedItems };
    items.forEach(item => {
      const itemId = item.id || item.uri;
      newSelectedItems[itemId] = !areAllSelected;
    });
    
    setSelectedItems(newSelectedItems);
    
    setItems(
      items.map((item) => ({ ...item, selected: !areAllSelected }))
    );
  };
  
  // Get total selected size
  const getTotalSelectedSize = (): number => {
    return items.reduce((total, item) => {
      const itemId = item.id || item.uri;
      return selectedItems[itemId] ? total + item.size : total;
    }, 0);
  };
  
  // Get count of selected items
  const getSelectedCount = (): number => {
    return Object.values(selectedItems).filter(selected => selected).length;
  };
  
  // Clean selected items
  const cleanSelectedItems = async () => {
    const selectedItemsList = items.filter((item) => {
      const itemId = item.id || item.uri;
      return selectedItems[itemId];
    });
    
    if (selectedItemsList.length === 0) {
      Alert.alert('No items selected', 'Please select items to clean.');
      return;
    }
    
    // Check for system files that shouldn't be deleted
    const systemItems = selectedItemsList.filter(item => 
      item.name.includes('System') || 
      item.name.includes('OS') || 
      item.description.includes('essential') ||
      item.description.includes('required')
    );
    
    if (systemItems.length > 0) {
      Alert.alert(
        'Warning: System Files Selected',
        'Some selected items appear to be system files that are required for proper device operation. Removing these files may cause system instability.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Review Selection',
            onPress: () => {
              // Deselect system files
              const newSelectedItems = { ...selectedItems };
              
              systemItems.forEach(item => {
                const itemId = item.id || item.uri;
                newSelectedItems[itemId] = false;
              });
              
              setSelectedItems(newSelectedItems);
              
              // Also update the items array for consistency
              setItems(
                items.map(item => 
                  systemItems.some(sysItem => (sysItem.id || sysItem.uri) === (item.id || item.uri)) 
                    ? { ...item, selected: false } 
                    : item
                )
              );
            }
          },
          {
            text: 'Proceed Anyway',
            style: 'destructive',
            onPress: () => proceedWithCleaning(selectedItemsList),
          },
        ]
      );
    } else {
      proceedWithCleaning(selectedItemsList);
    }
  };
  
  const proceedWithCleaning = (selectedItems: CleanItem[]) => {
    Alert.alert(
      'Clean Selected Items',
      `Are you sure you want to clean ${selectedItems.length} items (${formatBytes(getTotalSelectedSize())})?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clean',
          style: 'destructive',
          onPress: async () => {
            setIsCleaning(true);
            
            try {
              // Actually delete the files
              const result = await cleanFiles(selectedItems);
              
              // Remove cleaned items from the list
              setItems(items.filter((item) => !item.selected));
              
              // Show success message
              Alert.alert(
                'Cleaning Complete',
                `Successfully removed ${result.success} files (${formatBytes(getTotalSelectedSize())})${
                  result.failed > 0 ? `\nFailed to remove ${result.failed} files.` : ''
                }`
              );
            } catch (error) {
              console.error('Error cleaning files:', error);
              Alert.alert(
                'Cleaning Error',
                'An error occurred while cleaning files. Some files may not have been removed.'
              );
            } finally {
              setIsCleaning(false);
            }
          },
        },
      ]
    );
  };
  
  // Render clean item
  const renderCleanItem = ({ item }: { item: CleanItem }) => {
    const itemId = item.id || item.uri;
    const isSelected = selectedItems[itemId] || false;
    
    return (
      <View style={[styles.cleanItem, { backgroundColor: cardColor }]}>
        <View style={styles.cleanItemInfo}>
          <Text style={[styles.cleanItemName, { color: textColor }]}>{item.name}</Text>
          <Text style={[styles.cleanItemDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
            {item.description}
          </Text>
          <Text style={[styles.cleanItemSize, { color: accentColor }]}>
            {formatBytes(item.size)}
          </Text>
        </View>
        
        <Switch
          value={isSelected}
          onValueChange={() => toggleItemSelection(itemId)}
          trackColor={{ false: '#767577', true: isDarkMode ? '#4A90E2' : '#0A7EA4' }}
          thumbColor={isSelected ? '#FFFFFF' : '#F4F3F4'}
        />
      </View>
    );
  };

  // Helper function to get a friendly name for each category
  const getCategoryName = (category: CleanCategory): string => {
    switch (category) {
      case 'junk':
        return 'Junk Files';
      case 'cache':
        return 'Cache Files';
      case 'appData':
        return 'App Data';
      case 'downloads':
        return 'Downloads';
      default:
        return 'Files';
    }
  };

  // Check if all items are selected
  const isAllSelected = (): boolean => {
    const selectedCount = getSelectedCount();
    return selectedCount > 0 && selectedCount === items.length;
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: textColor }]}>Clean Storage</Text>
      </View>
      
      {/* Category Selection */}
      <View style={styles.categorySelector}>
        <CategoryButton
          title="Junk Files"
          isSelected={activeCategory === 'junk'}
          onPress={() => setActiveCategory('junk')}
          icon="trash.fill"
          isDarkMode={isDarkMode}
        />
        <CategoryButton
          title="Cache Data"
          isSelected={activeCategory === 'cache'}
          onPress={() => setActiveCategory('cache')}
          icon="arrow.triangle.2.circlepath"
          isDarkMode={isDarkMode}
        />
        <CategoryButton
          title="App Data"
          isSelected={activeCategory === 'appData'}
          onPress={() => setActiveCategory('appData')}
          icon="app.fill"
          isDarkMode={isDarkMode}
        />
        <CategoryButton
          title="Downloads"
          isSelected={activeCategory === 'downloads'}
          onPress={() => setActiveCategory('downloads')}
          icon="arrow.down.circle.fill"
          isDarkMode={isDarkMode}
        />
      </View>
      
      {/* Scan Button */}
      <TouchableOpacity
        style={[
          styles.scanButton,
          { 
            backgroundColor: accentColor, 
            opacity: isLoading ? 0.7 : 1,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
            elevation: 2
          }
        ]}
        onPress={scanForCleanableItems}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color={isDarkMode ? Colors.dark.background : "#FFFFFF"} />
        ) : (
          <Text style={[styles.scanButtonText, { 
            fontWeight: '600',
            color: isDarkMode ? Colors.dark.background : '#FFFFFF' 
          }]}>
            {scanCompleted ? 'Rescan' : 'Scan Now'}
          </Text>
        )}
      </TouchableOpacity>
      
      {/* Results */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={accentColor} />
          <Text style={[styles.loadingText, { color: textColor }]}>
            Scanning for {getCategoryName(activeCategory)}...
          </Text>
        </View>
      ) : items.length > 0 ? (
        <>
          <View style={styles.actionBar}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: accentColor }]}
              onPress={toggleSelectAll}
            >
              <Text style={[styles.actionButtonText, { color: '#FFFFFF' }]}>
                {isAllSelected() ? 'Deselect All' : 'Select All'}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.actionButton,
                { 
                  backgroundColor: getSelectedCount() > 0 ? '#FF3B30' : '#888',
                  opacity: getSelectedCount() > 0 ? 1 : 0.5
                }
              ]}
              onPress={cleanSelectedItems}
              disabled={getSelectedCount() === 0}
            >
              <Text style={[styles.actionButtonText, { color: '#FFFFFF' }]}>
                Clean ({getSelectedCount()}) - {formatBytes(getTotalSelectedSize())}
              </Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={items}
            renderItem={renderCleanItem}
            keyExtractor={(item) => item.id || item.uri}
            contentContainerStyle={styles.list}
          />
        </>
      ) : noItemsFound ? (
        <View style={styles.emptyStateContainer}>
          <IconSymbol name="checkmark.circle.fill" size={64} color={accentColor} />
          <Text style={[styles.emptyStateTitle, { color: textColor }]}>
            All Clear!
          </Text>
          <Text style={[styles.emptyStateMessage, { color: textColor }]}>
            No {getCategoryName(activeCategory).toLowerCase()} found that can be cleaned.
            Your device is already optimized in this category.
          </Text>
        </View>
      ) : (
        <View style={styles.emptyStateContainer}>
          <IconSymbol name="arrow.up.circle.fill" size={64} color={accentColor} />
          <Text style={[styles.emptyStateTitle, { color: textColor }]}>
            Ready to Clean
          </Text>
          <Text style={[styles.emptyStateMessage, { color: textColor }]}>
            Tap "Scan" above to find {getCategoryName(activeCategory).toLowerCase()} that can be cleaned.
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
}

// Category Button Component
function CategoryButton({
  title,
  isSelected,
  onPress,
  icon,
  isDarkMode,
}: {
  title: string;
  isSelected: boolean;
  onPress: () => void;
  icon: string;
  isDarkMode: boolean;
}) {
  return (
    <TouchableOpacity
      style={[
        styles.categoryButton,
        {
          backgroundColor: isSelected 
            ? (isDarkMode ? '#555' : '#0A7EA4') 
            : (isDarkMode ? '#333' : '#DDDDDD'),
          borderWidth: 1,
          borderColor: isSelected 
            ? (isDarkMode ? '#777' : '#0A7EA4') 
            : (isDarkMode ? '#444' : '#CCCCCC'),
        },
      ]}
      onPress={onPress}
    >
      <IconSymbol
        name={icon}
        size={20}
        color={isSelected || isDarkMode ? '#FFFFFF' : (isSelected ? '#FFFFFF' : '#333333')}
      />
      <Text
        style={[
          styles.categoryButtonText,
          { 
            color: isSelected || isDarkMode ? '#FFFFFF' : '#333333',
            fontWeight: '600'
          },
        ]}
        numberOfLines={1}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  categorySelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 8,
    width: '48%',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  categoryButtonText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  scanButton: {
    marginHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  scanButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    marginBottom: 20,
  },
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 10,
  },
  selectAllButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
  },
  selectAllText: {
    fontWeight: '500',
  },
  selectedInfo: {
    fontSize: 14,
  },
  cleanList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  cleanItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    marginBottom: 10,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cleanItemInfo: {
    flex: 1,
    marginRight: 12,
  },
  cleanItemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  cleanItemDescription: {
    fontSize: 14,
    marginBottom: 4,
  },
  cleanItemSize: {
    fontSize: 14,
    fontWeight: '500',
  },
  cleanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    padding: 14,
    borderRadius: 10,
  },
  cleanButtonText: {
    marginLeft: 8,
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 40,
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 16,
  },
  actionButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
  },
  actionButtonText: {
    fontWeight: '500',
  },
  list: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  emptyStateContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyStateTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateMessage: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    opacity: 0.8,
  },
}); 