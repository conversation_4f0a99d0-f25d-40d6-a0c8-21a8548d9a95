import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert, Dimensions, SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { PieChart } from 'react-native-chart-kit';

import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import { FileInfo, findDuplicateFiles, findOldFiles, formatBytes, getLargeFiles, getMediaFiles, getStorageBreakdown, getStorageInfo } from '@/utils/storageUtils';

const screenWidth = Dimensions.get('window').width;

type AnalysisType = 'overview' | 'largeFiles' | 'duplicateFiles' | 'oldFiles';

interface StorageData {
  name: string;
  size: number;
  color: string;
  legendFontColor: string;
  legendFontSize: number;
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  action: () => void;
  estimatedSavings?: string;
}

interface SmartRecommendation {
  id: string;
  type: 'warning' | 'suggestion' | 'info';
  title: string;
  description: string;
  action: string;
  onPress: () => void;
}

export default function EnhancedAnalyzeScreen() {
  const { t } = useTranslation();
  const { isDarkMode, isAIEnabled } = useAppContext();
  const router = useRouter();

  const [analysisType, setAnalysisType] = useState<AnalysisType>('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [storageData, setStorageData] = useState<StorageData[]>([]);
  const [largeFiles, setLargeFiles] = useState<FileInfo[]>([]);
  const [duplicateFiles, setDuplicateFiles] = useState<FileInfo[]>([]);
  const [oldFiles, setOldFiles] = useState<FileInfo[]>([]);
  const [quickActions, setQuickActions] = useState<QuickAction[]>([]);
  const [recommendations, setRecommendations] = useState<SmartRecommendation[]>([]);
  const [storageInfo, setStorageInfo] = useState<any>(null);

  const colorScheme = isDarkMode ? 'dark' : 'light';
  const backgroundColor = Colors[colorScheme].background;
  const textColor = Colors[colorScheme].text;
  const accentColor = Colors[colorScheme].tint;
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';

  // Load storage overview data
  const loadStorageOverview = async () => {
    setIsLoading(true);
    try {
      // Get storage info
      const info = await getStorageInfo();
      setStorageInfo(info);

      // Get storage breakdown
      const breakdown = await getStorageBreakdown();
      
      const chartData: StorageData[] = [
        {
          name: t('analyze.images'),
          size: breakdown.images,
          color: '#FF6B6B',
          legendFontColor: textColor,
          legendFontSize: 12,
        },
        {
          name: t('analyze.videos'),
          size: breakdown.videos,
          color: '#4ECDC4',
          legendFontColor: textColor,
          legendFontSize: 12,
        },
        {
          name: t('analyze.audio'),
          size: breakdown.audio,
          color: '#45B7D1',
          legendFontColor: textColor,
          legendFontSize: 12,
        },
        {
          name: t('analyze.documents'),
          size: breakdown.documents,
          color: '#96CEB4',
          legendFontColor: textColor,
          legendFontSize: 12,
        },
        {
          name: t('analyze.apps'),
          size: breakdown.apps,
          color: '#FFEAA7',
          legendFontColor: textColor,
          legendFontSize: 12,
        },
        {
          name: t('analyze.other'),
          size: breakdown.other,
          color: '#DDA0DD',
          legendFontColor: textColor,
          legendFontSize: 12,
        },
      ].filter(item => item.size > 0);

      setStorageData(chartData);

      // Load analysis data
      const [large, media] = await Promise.all([
        getLargeFiles(),
        getMediaFiles()
      ]);

      setLargeFiles(large.slice(0, 10)); // Top 10 large files

      // Find duplicates
      if (media.length > 0) {
        const duplicates = await findDuplicateFiles(media);
        setDuplicateFiles(Object.values(duplicates).flat().slice(0, 10));
      }

      // Find old files
      const old = findOldFiles(media, 3); // 3 months
      setOldFiles(old.slice(0, 10));

      // Generate quick actions
      generateQuickActions(large, Object.values(await findDuplicateFiles(media)).flat(), old);

      // Generate smart recommendations
      generateSmartRecommendations(breakdown, large, Object.values(await findDuplicateFiles(media)).flat());

    } catch (error) {
      console.error('Error loading storage overview:', error);
      Alert.alert(t('analyze.error'), t('analyze.errorMessage'));
    } finally {
      setIsLoading(false);
    }
  };

  const generateQuickActions = (large: FileInfo[], duplicates: FileInfo[], old: FileInfo[]) => {
    const actions: QuickAction[] = [];

    if (large.length > 0) {
      const totalSize = large.reduce((sum, file) => sum + file.size, 0);
      actions.push({
        id: 'compress_large',
        title: t('analyze.compressLargeFiles'),
        description: `${large.length} files > 50MB`,
        icon: 'arrow.down.circle.fill',
        color: '#FF6B6B',
        estimatedSavings: formatBytes(totalSize * 0.3),
        action: () => router.push('/(tabs)/compress')
      });
    }

    if (duplicates.length > 0) {
      const totalSize = duplicates.reduce((sum, file) => sum + file.size, 0);
      actions.push({
        id: 'remove_duplicates',
        title: t('analyze.removeDuplicates'),
        description: `${duplicates.length} duplicate files`,
        icon: 'doc.on.doc.fill',
        color: '#4ECDC4',
        estimatedSavings: formatBytes(totalSize * 0.5),
        action: () => setAnalysisType('duplicateFiles')
      });
    }

    if (old.length > 0) {
      const totalSize = old.reduce((sum, file) => sum + file.size, 0);
      actions.push({
        id: 'clean_old',
        title: t('analyze.cleanOldFiles'),
        description: `${old.length} files > 3 months old`,
        icon: 'clock.fill',
        color: '#96CEB4',
        estimatedSavings: formatBytes(totalSize),
        action: () => setAnalysisType('oldFiles')
      });
    }

    actions.push({
      id: 'deep_clean',
      title: t('analyze.deepClean'),
      description: t('analyze.deepCleanDescription'),
      icon: 'sparkles',
      color: '#45B7D1',
      action: () => router.push('/(tabs)/clean')
    });

    setQuickActions(actions);
  };

  const generateSmartRecommendations = (breakdown: any, large: FileInfo[], duplicates: FileInfo[]) => {
    const recs: SmartRecommendation[] = [];

    // Check if images take up too much space
    if (breakdown.images > breakdown.videos && breakdown.images > 1024 * 1024 * 1024) { // > 1GB
      recs.push({
        id: 'compress_images',
        type: 'suggestion',
        title: t('analyze.tooManyImages'),
        description: t('analyze.tooManyImagesDesc', { size: formatBytes(breakdown.images) }),
        action: t('analyze.compressImages'),
        onPress: () => router.push('/(tabs)/compress')
      });
    }

    // Check for large files
    if (large.length > 5) {
      recs.push({
        id: 'large_files_warning',
        type: 'warning',
        title: t('analyze.largeFilesWarning'),
        description: t('analyze.largeFilesWarningDesc', { count: large.length }),
        action: t('analyze.reviewLargeFiles'),
        onPress: () => setAnalysisType('largeFiles')
      });
    }

    // Check for duplicates
    if (duplicates.length > 3) {
      recs.push({
        id: 'duplicates_info',
        type: 'info',
        title: t('analyze.duplicatesFound'),
        description: t('analyze.duplicatesFoundDesc', { count: duplicates.length }),
        action: t('analyze.reviewDuplicates'),
        onPress: () => setAnalysisType('duplicateFiles')
      });
    }

    setRecommendations(recs);
  };

  useEffect(() => {
    if (analysisType === 'overview') {
      loadStorageOverview();
    }
  }, [analysisType]);

  const renderStorageChart = () => {
    if (storageData.length === 0) return null;

    return (
      <View style={[styles.chartContainer, { backgroundColor: cardColor }]}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          {t('analyze.storageBreakdown')}
        </Text>
        <PieChart
          data={storageData}
          width={screenWidth - 64}
          height={220}
          chartConfig={{
            backgroundColor: cardColor,
            backgroundGradientFrom: cardColor,
            backgroundGradientTo: cardColor,
            color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
          }}
          accessor="size"
          backgroundColor="transparent"
          paddingLeft="15"
          absolute
        />
      </View>
    );
  };

  const renderQuickActions = () => {
    if (quickActions.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          {t('analyze.quickActions')}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {quickActions.map((action) => (
            <TouchableOpacity
              key={action.id}
              style={[styles.quickActionCard, { backgroundColor: cardColor }]}
              onPress={action.action}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: action.color }]}>
                <IconSymbol name={action.icon} size={24} color="#FFFFFF" />
              </View>
              <Text style={[styles.quickActionTitle, { color: textColor }]}>
                {action.title}
              </Text>
              <Text style={[styles.quickActionDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                {action.description}
              </Text>
              {action.estimatedSavings && (
                <Text style={[styles.quickActionSavings, { color: action.color }]}>
                  Save ~{action.estimatedSavings}
                </Text>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderSmartRecommendations = () => {
    if (recommendations.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          {t('analyze.smartRecommendations')}
        </Text>
        {recommendations.map((rec) => (
          <TouchableOpacity
            key={rec.id}
            style={[styles.recommendationCard, { backgroundColor: cardColor }]}
            onPress={rec.onPress}
          >
            <View style={styles.recommendationContent}>
              <View style={[styles.recommendationIcon, { 
                backgroundColor: rec.type === 'warning' ? '#FF6B6B' : 
                                rec.type === 'suggestion' ? '#4ECDC4' : '#45B7D1' 
              }]}>
                <IconSymbol 
                  name={rec.type === 'warning' ? 'exclamationmark.triangle.fill' : 
                       rec.type === 'suggestion' ? 'lightbulb.fill' : 'info.circle.fill'} 
                  size={16} 
                  color="#FFFFFF" 
                />
              </View>
              <View style={styles.recommendationText}>
                <Text style={[styles.recommendationTitle, { color: textColor }]}>
                  {rec.title}
                </Text>
                <Text style={[styles.recommendationDescription, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
                  {rec.description}
                </Text>
              </View>
            </View>
            <Text style={[styles.recommendationAction, { color: accentColor }]}>
              {rec.action}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: textColor }]}>{t('analyze.title')}</Text>
      </View>

      {/* Analysis Type Selector */}
      <View style={styles.analysisTypes}>
        {[
          { id: 'overview', label: t('analyze.overview'), icon: 'chart.pie.fill' },
          { id: 'largeFiles', label: t('analyze.largeFiles'), icon: 'doc.fill' },
          { id: 'duplicateFiles', label: t('analyze.duplicateFiles'), icon: 'doc.on.doc.fill' },
          { id: 'oldFiles', label: t('analyze.oldFiles'), icon: 'clock.fill' }
        ].map((type) => (
          <TouchableOpacity
            key={type.id}
            style={[
              styles.analysisButton,
              {
                backgroundColor: isDarkMode ?
                  (analysisType === type.id ? '#555' : '#333') :
                  (analysisType === type.id ? '#0A7EA4' : '#DDDDDD'),
              }
            ]}
            onPress={() => setAnalysisType(type.id as AnalysisType)}
          >
            <IconSymbol 
              name={type.icon} 
              size={16} 
              color={isDarkMode ? '#FFFFFF' : (analysisType === type.id ? '#FFFFFF' : '#333333')} 
            />
            <Text
              style={[
                styles.analysisButtonText,
                {
                  color: isDarkMode ?
                    '#FFFFFF' :
                    (analysisType === type.id ? '#FFFFFF' : '#333333'),
                }
              ]}
            >
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={accentColor} />
          <Text style={[styles.loadingText, { color: textColor }]}>
            {t('analyze.analyzing')}
          </Text>
        </View>
      ) : (
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {analysisType === 'overview' && (
            <>
              {storageInfo && (
                <View style={[styles.storageOverview, { backgroundColor: cardColor }]}>
                  <Text style={[styles.sectionTitle, { color: textColor }]}>
                    {t('analyze.storageUsage')}
                  </Text>
                  <View style={styles.storageStats}>
                    <View style={styles.storageStat}>
                      <Text style={[styles.storageStatValue, { color: '#FF6B6B' }]}>
                        {formatBytes(storageInfo.usedSpace)}
                      </Text>
                      <Text style={[styles.storageStatLabel, { color: textColor }]}>
                        {t('analyze.used')}
                      </Text>
                    </View>
                    <View style={styles.storageStat}>
                      <Text style={[styles.storageStatValue, { color: '#4ECDC4' }]}>
                        {formatBytes(storageInfo.freeSpace)}
                      </Text>
                      <Text style={[styles.storageStatLabel, { color: textColor }]}>
                        {t('analyze.free')}
                      </Text>
                    </View>
                    <View style={styles.storageStat}>
                      <Text style={[styles.storageStatValue, { color: textColor }]}>
                        {formatBytes(storageInfo.totalSpace)}
                      </Text>
                      <Text style={[styles.storageStatLabel, { color: textColor }]}>
                        {t('analyze.total')}
                      </Text>
                    </View>
                  </View>
                </View>
              )}
              
              {renderStorageChart()}
              {renderQuickActions()}
              {renderSmartRecommendations()}
            </>
          )}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  analysisTypes: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  analysisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 8,
    width: '48%',
  },
  analysisButtonText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  storageOverview: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  storageStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  storageStat: {
    alignItems: 'center',
  },
  storageStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  storageStatLabel: {
    fontSize: 12,
    marginTop: 4,
    opacity: 0.7,
  },
  chartContainer: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  quickActionCard: {
    width: 160,
    padding: 16,
    borderRadius: 12,
    marginRight: 12,
    alignItems: 'center',
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  quickActionDescription: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 8,
  },
  quickActionSavings: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  recommendationCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  recommendationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  recommendationIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  recommendationText: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  recommendationDescription: {
    fontSize: 12,
  },
  recommendationAction: {
    fontSize: 12,
    fontWeight: '600',
  },
});
