import Slider from '@react-native-community/slider';
import * as FileSystem from 'expo-file-system';
import { Image } from 'expo-image';
import * as MediaLibrary from 'expo-media-library';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert, FlatList, SafeAreaView, StyleSheet, Switch, Text, TouchableOpacity, View } from 'react-native';

import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import {
    FileInfo,
    compressImage,
    formatBytes,
    getDirectoryContents,
    getFileType,
    saveImageToMediaLibrary
} from '@/utils/storageUtils';

type MediaCategory = 'images' | 'videos' | 'documents';

export default function CompressScreen() {
  const { t } = useTranslation();
  const { isDarkMode } = useAppContext();

  const [activeCategory, setActiveCategory] = useState<MediaCategory>('images');
  const [isLoading, setIsLoading] = useState(false);
  const [isCompressing, setIsCompressing] = useState(false);
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [compressionLevel, setCompressionLevel] = useState(0.5); // 0.1 to 0.9
  const [loadError, setLoadError] = useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<Record<string, boolean>>({});
  const [compressProgress, setCompressProgress] = useState<{ current: number; total: number; filename: string } | null>(null);

  // Get color scheme based on dark mode
  const colorScheme = isDarkMode ? 'dark' : 'light';
  const backgroundColor = Colors[colorScheme].background;
  const textColor = Colors[colorScheme].text;
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';
  const accentColor = Colors[colorScheme].tint;

  // Function to load media files based on category
  const loadFiles = async () => {
    setIsLoading(true);
    setLoadError(null);

    try {
      let mediaFiles: FileInfo[] = [];

      switch (activeCategory) {
        case 'images':
          const imageAssets = await MediaLibrary.getAssetsAsync({
            mediaType: ['photo'],
            first: 100,
            sortBy: [MediaLibrary.SortBy.creationTime],
          });

          if (imageAssets.assets.length === 0) {
            setLoadError('No images found on your device.');
            setIsLoading(false);
            return;
          }

          // Convert assets to FileInfo format
          mediaFiles = await Promise.all(
            imageAssets.assets.map(async (asset) => {
              // Try to get more accurate file information where possible
              let fileSize = 0;
              try {
                // Estimate size based on resolution
                fileSize = Math.round((asset.width * asset.height) / 3); // Reasonable JPEG compression estimate
              } catch (error) {
                console.warn('Error estimating image size:', error);
                // Default to a reasonable size if estimation fails
                fileSize = 1024 * 1024; // 1MB default
              }

              return {
                id: asset.id,
                name: asset.filename || `image_${asset.id}.jpg`,
                path: asset.uri,
                uri: asset.uri,
                size: fileSize,
                modificationTime: asset.modificationTime,
                type: 'image',
                selected: false,
              };
            })
          );
          break;

        case 'videos':
          const videoAssets = await MediaLibrary.getAssetsAsync({
            mediaType: ['video'],
            first: 50,
            sortBy: [MediaLibrary.SortBy.creationTime],
          });

          if (videoAssets.assets.length === 0) {
            setLoadError('No videos found on your device.');
            setIsLoading(false);
            return;
          }

          // Convert assets to FileInfo format with more accurate size estimates
          mediaFiles = videoAssets.assets.map(asset => {
            // Estimate video size based on duration and resolution
            const estimatedSize = asset.duration * 2 * 1024 * 1024; // ~2MB per second for HD video

            return {
              id: asset.id,
              name: asset.filename || `video_${asset.id}.mp4`,
              path: asset.uri,
              uri: asset.uri,
              size: estimatedSize,
              modificationTime: asset.modificationTime,
              type: 'video',
              selected: false,
            };
          });
          break;

        case 'documents':
          try {
            // Attempt to scan document directories
            const documentDir = FileSystem.documentDirectory;
            let docFiles: FileInfo[] = [];

            if (documentDir) {
              const contents = await getDirectoryContents(documentDir);
              // Filter for document types
              docFiles = contents.filter(
                file => !file.isDirectory &&
                ['document', 'pdf', 'spreadsheet', 'presentation'].includes(getFileType(file.name))
              );
            }

            if (docFiles.length === 0) {
              setLoadError('No document files found in accessible locations.');
              setIsLoading(false);
              return;
            }

            mediaFiles = docFiles;
          } catch (error) {
            console.error('Error loading documents:', error);
            setLoadError('Error accessing document files.');
            setIsLoading(false);
            return;
          }
          break;

        default:
          setLoadError('Unsupported file category.');
          setIsLoading(false);
          return;
      }

      // Sort by size (largest first) for better compression impact
      mediaFiles.sort((a, b) => b.size - a.size);

      setFiles(mediaFiles);
      // Initialize selected files state
      const initialSelectedIds: Record<string, boolean> = {};
      mediaFiles.forEach(file => {
        const fileId = file.id || file.uri;
        initialSelectedIds[fileId] = false;
      });
      setSelectedFiles(initialSelectedIds);
    } catch (error) {
      console.error('Error loading files:', error);
      setLoadError(`Error loading files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle file selection
  const toggleFileSelection = (id?: string) => {
    if (!id) return;

    // Update the selectedFiles state
    setSelectedFiles(prev => {
      const newSelectedFiles = { ...prev };
      newSelectedFiles[id] = !prev[id];
      return newSelectedFiles;
    });

    // Also update the selected property in the files array for UI consistency
    setFiles(
      files.map((file) => {
        const fileId = file.id || file.uri;
        if (fileId === id) {
          return { ...file, selected: !file.selected };
        }
        return file;
      })
    );
  };

  // Toggle all files selection
  const toggleSelectAll = () => {
    const areAllSelected = files.every((file) => {
      const fileId = file.id || file.uri;
      return selectedFiles[fileId];
    });

    // Update selectedFiles state
    const newSelectedItems: Record<string, boolean> = {};
    files.forEach(file => {
      const fileId = file.id || file.uri;
      newSelectedItems[fileId] = !areAllSelected;
    });
    setSelectedFiles(newSelectedItems);

    // Also update the files array
    setFiles(
      files.map((file) => ({ ...file, selected: !areAllSelected }))
    );
  };

  // Calculate potential savings based on compression level
  const calculatePotentialSavings = (): number => {
    const selectedFiles = files.filter(file => file.selected);
    const imageFiles = selectedFiles.filter(file => file.type === 'image');
    const totalSize = imageFiles.reduce((sum, file) => sum + file.size, 0);

    // Estimate compressed size based on compression level
    // This is a more accurate estimation based on common compression ratios
    const compressionRatio = compressionLevel < 0.4 ? 0.8 :
                            compressionLevel < 0.7 ? 0.6 : 0.3;
    const estimatedSavings = totalSize * compressionRatio;

    return estimatedSavings;
  };

  // Compress selected files with progress reporting
  const compressSelectedFiles = async () => {
    const filesToCompress = files.filter(file => {
      const fileId = file.id || file.uri;
      return selectedFiles[fileId];
    });

    if (filesToCompress.length === 0) {
      Alert.alert('No Files Selected', 'Please select files to compress first.');
      return;
    }

    // Confirm before compression
    Alert.alert(
      'Compress Files',
      `Are you sure you want to compress ${filesToCompress.length} files? This will create optimized copies of your files.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Compress',
          onPress: async () => {
            setIsCompressing(true);

            // Calculate total size before compression
            const totalSizeBefore = filesToCompress.reduce((sum, file) => sum + file.size, 0);
            let totalSizeAfter = 0;
            let successCount = 0;
            let failCount = 0;

            // Process files one by one
            for (let i = 0; i < filesToCompress.length; i++) {
              const file = filesToCompress[i];
              setCompressProgress({
                current: i + 1,
                total: filesToCompress.length,
                filename: file.name
              });

              try {
                if (file.type === 'image') {
                  // Only images can be compressed with our current implementation
                  const compressedUri = await compressImage(file.uri, compressionLevel);

                  if (compressedUri) {
                    // Get the size of the compressed file
                    try {
                      const fileInfo = await FileSystem.getInfoAsync(compressedUri);
                      if (fileInfo.exists && fileInfo.size) {
                        // Save the compressed image to the media library
                        const savedAssetId = await saveImageToMediaLibrary(
                          compressedUri,
                          'Compressed Images'
                        );

                        if (savedAssetId) {
                          totalSizeAfter += fileInfo.size;
                          successCount++;
                        } else {
                          console.warn(`Failed to save compressed image to media library: ${file.name}`);
                          failCount++;
                        }
                      } else {
                        console.warn(`Compressed file info not available: ${compressedUri}`);
                        failCount++;
                      }
                    } catch (error) {
                      console.error('Error getting compressed file info:', error);
                      failCount++;
                    }
                  } else {
                    console.warn(`Compression failed for file: ${file.name}`);
                    failCount++;
                  }
                } else {
                  // Skip non-image files for now
                  console.log(`Skipping non-image file: ${file.name} (${file.type})`);
                  totalSizeAfter += file.size; // No change
                  failCount++;
                }
              } catch (error) {
                console.error(`Error compressing file ${file.name}:`, error);
                failCount++;
              }
            }

            setIsCompressing(false);
            setCompressProgress(null);

            // Calculate space saved
            const spaceSaved = totalSizeBefore - totalSizeAfter;

            // Show results
            Alert.alert(
              'Compression Complete',
              `Successfully compressed ${successCount} files.\nFailed to compress ${failCount} files.\nSpace saved: ${formatBytes(spaceSaved > 0 ? spaceSaved : 0)}`,
              [{ text: 'OK' }]
            );
          },
        },
      ]
    );
  };

  // Add handleCompression function
  const handleCompression = async () => {
    const selectedFiles = files.filter(file => file.selected);

    if (selectedFiles.length === 0) {
      Alert.alert('No Files Selected', 'Please select files to compress.');
      return;
    }

    // Check for system files that shouldn't be modified
    const systemFiles = selectedFiles.filter(file =>
      file.name.includes('system') ||
      file.name.includes('config') ||
      file.uri.includes('/system/') ||
      file.uri.includes('/Android/data/system/')
    );

    if (systemFiles.length > 0) {
      Alert.alert(
        'System Files Detected',
        'Some selected files appear to be system files. Modifying these files may cause system instability.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Review Selection',
            onPress: () => {
              // Deselect system files
              setFiles(
                files.map(file =>
                  systemFiles.some(sysFile => sysFile.id === file.id)
                    ? { ...file, selected: false }
                    : file
                )
              );
            }
          },
          {
            text: 'Proceed Anyway',
            style: 'destructive',
            onPress: () => compressSelectedFiles(),
          },
        ]
      );
    } else {
      compressSelectedFiles();
    }
  };

  // Render file item
  const renderFileItem = ({ item }: { item: FileInfo }) => (
    <View style={[styles.fileItem, { backgroundColor: cardColor }]}>
      <View style={styles.filePreview}>
        {item.type === 'image' ? (
          <Image
            source={{ uri: item.uri }}
            style={styles.filePreviewImage}
            contentFit="cover"
          />
        ) : (
          <IconSymbol
            name={item.type === 'video' ? 'video.fill' : 'doc.fill'}
            size={24}
            color="#9BA1A6"
          />
        )}
      </View>

      <View style={styles.fileDetails}>
        <Text style={[styles.fileName, { color: textColor }]} numberOfLines={1}>
          {item.name}
        </Text>
        <Text style={[styles.fileInfo, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
          {formatBytes(item.size)}
        </Text>
      </View>

      <Switch
        value={item.selected}
        onValueChange={() => toggleFileSelection(item.id)}
        trackColor={{ false: '#767577', true: isDarkMode ? '#4A90E2' : '#0A7EA4' }}
        thumbColor={item.selected ? '#FFFFFF' : '#F4F3F4'}
      />
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: textColor }]}>{t('compress.title')}</Text>
      </View>

      {/* Category Selection */}
      <View style={styles.categorySelector}>
        <CategoryButton
          title={t('compress.images')}
          isSelected={activeCategory === 'images'}
          onPress={() => setActiveCategory('images')}
          icon="photo.fill"
          isDarkMode={isDarkMode}
        />
        <CategoryButton
          title={t('compress.videos')}
          isSelected={activeCategory === 'videos'}
          onPress={() => setActiveCategory('videos')}
          icon="video.fill"
          isDarkMode={isDarkMode}
        />
        <CategoryButton
          title={t('compress.documents')}
          isSelected={activeCategory === 'documents'}
          onPress={() => setActiveCategory('documents')}
          icon="doc.fill"
          isDarkMode={isDarkMode}
        />
      </View>

      {/* Load Files Button */}
      <TouchableOpacity
        style={[
          styles.loadButton,
          {
            backgroundColor: accentColor,
            opacity: isLoading ? 0.7 : 1,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
            elevation: 2
          }
        ]}
        onPress={loadFiles}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color={isDarkMode ? Colors.dark.background : "#FFFFFF"} />
        ) : (
          <Text style={[styles.loadButtonText, {
            fontWeight: '600',
            color: isDarkMode ? Colors.dark.background : '#FFFFFF'
          }]}>
{t('compress.load')} {activeCategory === 'images' ? t('compress.images') : activeCategory === 'videos' ? t('compress.videos') : t('compress.documents')}
          </Text>
        )}
      </TouchableOpacity>

      {/* Compression Level */}
      {files.length > 0 && (
        <View style={[styles.compressionLevelContainer, { backgroundColor: cardColor }]}>
          <Text style={[styles.compressionLevelTitle, { color: textColor }]}>
            {t('compress.compressionLevel')}
          </Text>

          <View style={styles.compressionLevelSlider}>
            <Text style={[styles.sliderLabel, { color: textColor, fontWeight: '500' }]}>{t('compress.light')}</Text>
            <Slider
              style={{ flex: 1, height: 40 }}
              minimumValue={0.1}
              maximumValue={0.9}
              step={0.1}
              value={compressionLevel}
              onValueChange={setCompressionLevel}
              minimumTrackTintColor={accentColor}
              maximumTrackTintColor={isDarkMode ? '#555' : '#E5E5EA'}
              thumbTintColor={accentColor}
            />
            <Text style={[styles.sliderLabel, { color: textColor, fontWeight: '500' }]}>{t('compress.heavy')}</Text>
          </View>

          <Text style={[styles.compressionInfo, { color: isDarkMode ? '#9BA1A6' : '#687076' }]}>
            {compressionLevel < 0.4
              ? t('compress.highCompressionNote')
              : compressionLevel > 0.7
              ? t('compress.lowCompressionNote')
              : t('compress.balancedCompressionNote')}
          </Text>
        </View>
      )}

      {/* Files List */}
      {files.length > 0 && (
        <View style={styles.filesContainer}>
          <View style={styles.actionBar}>
            <TouchableOpacity
              style={[
                styles.selectAllButton,
                {
                  backgroundColor: isDarkMode ? '#444' : '#0A7EA4',
                  borderColor: isDarkMode ? '#555' : '#0A7EA4'
                }
              ]}
              onPress={toggleSelectAll}
            >
              <Text style={[styles.selectAllText, {
                color: '#FFFFFF',
                fontWeight: '600'
              }]}>
                {t('compress.selectAll')}
              </Text>
            </TouchableOpacity>

            <Text style={[styles.selectedInfo, { color: textColor }]}>
              {t('compress.selectedCount', { selected: files.filter(f => f.selected).length, total: files.length })}
            </Text>
          </View>

          <FlatList
            data={files}
            renderItem={renderFileItem}
            keyExtractor={(item) => item.id || item.uri}
            contentContainerStyle={styles.filesList}
          />

          <TouchableOpacity
            style={[
              styles.compressButton,
              {
                backgroundColor: accentColor,
                opacity: !files.some(f => f.selected) || isCompressing ? 0.5 : 1,
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 3,
                elevation: 2
              },
            ]}
            onPress={handleCompression}
            disabled={!files.some(f => f.selected) || isCompressing}
          >
            {isCompressing ? (
              <ActivityIndicator color={isDarkMode ? Colors.dark.background : "#FFFFFF"} />
            ) : (
              <Text style={[styles.compressButtonText, {
                fontWeight: '600',
                color: isDarkMode ? Colors.dark.background : '#FFFFFF'
              }]}>
                {t('compress.compressNow')}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* No Files Loaded Message */}
      {files.length === 0 && !isLoading && (
        <View style={styles.noFilesContainer}>
          <IconSymbol
            name={activeCategory === 'images' ? "photo.fill" : activeCategory === 'videos' ? "video.fill" : "doc.fill"}
            size={48}
            color={accentColor}
          />
          <Text style={[styles.noFilesText, { color: textColor, fontWeight: 'bold', marginTop: 16, marginBottom: 8 }]}>
            {t('compress.noFilesLoaded', { category: activeCategory === 'images' ? t('compress.images') : activeCategory === 'videos' ? t('compress.videos') : t('compress.documents') })}
          </Text>
          <Text style={[styles.noFilesText, { color: textColor }]}>
            {loadError || t('compress.noFilesLoaded', { category: activeCategory === 'images' ? t('compress.images') : activeCategory === 'videos' ? t('compress.videos') : t('compress.documents') })}
          </Text>
          <View style={styles.instructionsContainer}>
            <Text style={[styles.instructionsTitle, { color: textColor, fontWeight: 'bold', marginTop: 24 }]}>
              How to compress files:
            </Text>
            <Text style={[styles.instructionText, { color: textColor }]}>
              1. Select a category above (Photos recommended)
            </Text>
            <Text style={[styles.instructionText, { color: textColor }]}>
              2. Load files using the button above
            </Text>
            <Text style={[styles.instructionText, { color: textColor }]}>
              3. Select the files you want to compress
            </Text>
            <Text style={[styles.instructionText, { color: textColor }]}>
              4. Adjust compression level as needed
            </Text>
            <Text style={[styles.instructionText, { color: textColor }]}>
              5. Tap "Compress Now" to optimize your files
            </Text>
          </View>
        </View>
      )}

      {/* Compression Progress Indicator */}
      {isCompressing && compressProgress && (
        <View style={styles.progressOverlay}>
          <View style={[styles.progressContainer, { backgroundColor: isDarkMode ? '#333' : '#fff' }]}>
            <ActivityIndicator size="large" color={accentColor} />
            <Text style={[styles.progressTitle, { color: textColor }]}>
              Compressing Files
            </Text>
            <Text style={[styles.progressText, { color: textColor }]}>
              File {compressProgress.current} of {compressProgress.total}
            </Text>
            <Text style={[styles.progressFilename, { color: isDarkMode ? '#9BA1A6' : '#687076' }]} numberOfLines={1}>
              {compressProgress.filename}
            </Text>
            <View style={styles.progressBarContainer}>
              <View
                style={[
                  styles.progressBar,
                  {
                    backgroundColor: accentColor,
                    width: `${(compressProgress.current / compressProgress.total) * 100}%`
                  }
                ]}
              />
            </View>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

// Category Button Component
function CategoryButton({
  title,
  isSelected,
  onPress,
  icon,
  isDarkMode,
}: {
  title: string;
  isSelected: boolean;
  onPress: () => void;
  icon: string;
  isDarkMode: boolean;
}) {
  return (
    <TouchableOpacity
      style={[
        styles.categoryButton,
        {
          backgroundColor: isSelected
            ? (isDarkMode ? '#555' : '#0A7EA4')
            : (isDarkMode ? '#333' : '#DDDDDD'),
          borderWidth: 1,
          borderColor: isSelected
            ? (isDarkMode ? '#777' : '#0A7EA4')
            : (isDarkMode ? '#444' : '#CCCCCC'),
        },
      ]}
      onPress={onPress}
    >
      <IconSymbol
        name={icon}
        size={20}
        color={isSelected || isDarkMode ? '#FFFFFF' : (isSelected ? '#FFFFFF' : '#333333')}
      />
      <Text
        style={[
          styles.categoryButtonText,
          {
            color: isSelected || isDarkMode ? '#FFFFFF' : '#333333',
            fontWeight: '600'
          },
        ]}
        numberOfLines={1}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  categorySelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    width: '30%',
    borderWidth: 1,
    borderColor: 'transparent',
    justifyContent: 'center',
  },
  categoryButtonText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  loadButton: {
    marginHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  loadButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  compressionLevelContainer: {
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 10,
    marginBottom: 16,
  },
  compressionLevelTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  compressionLevelSlider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sliderLabel: {
    fontSize: 12,
    width: 40,
    textAlign: 'center',
  },
  compressionInfo: {
    fontSize: 14,
    textAlign: 'center',
  },
  filesContainer: {
    flex: 1,
    marginBottom: 20,
  },
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 10,
  },
  selectAllButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
  },
  selectAllText: {
    fontWeight: '500',
  },
  selectedInfo: {
    fontSize: 14,
  },
  filesList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  fileItem: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  filePreview: {
    width: 50,
    height: 50,
    borderRadius: 8,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#2C2C2E',
    overflow: 'hidden',
  },
  filePreviewImage: {
    width: 50,
    height: 50,
  },
  fileDetails: {
    flex: 1,
    marginRight: 12,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  fileInfo: {
    fontSize: 14,
  },
  compressButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    padding: 14,
    borderRadius: 10,
  },
  compressButtonText: {
    marginLeft: 8,
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: 16,
  },
  noFilesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  noFilesText: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: 8,
  },
  progressOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  progressContainer: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  progressText: {
    fontSize: 16,
    marginBottom: 10,
  },
  progressFilename: {
    fontSize: 14,
    marginBottom: 10,
  },
  progressBarContainer: {
    width: '100%',
    height: 20,
    backgroundColor: '#E5E5EA',
    borderRadius: 10,
    marginBottom: 10,
  },
  progressBar: {
    height: '100%',
    borderRadius: 10,
  },
  instructionsContainer: {
    marginTop: 24,
    width: '100%',
    paddingHorizontal: 16,
  },
  instructionsTitle: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 16,
  },
  instructionText: {
    fontSize: 16,
    marginBottom: 8,
  },
});