import { router } from 'expo-router';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { SafeAreaView, ScrollView, StyleSheet, Switch, Text, TouchableOpacity, View } from 'react-native';

import { IconSymbol } from '@/components/ui/IconSymbol';
import { SettingsCard } from '@/components/ui/SettingsCard';
import { SettingsRow } from '@/components/ui/SettingsRow';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';

export default function SettingsScreen() {
  const { t } = useTranslation();
  const { isDarkMode, toggleDarkMode, isAIEnabled, toggleAIEnabled, language } = useAppContext();
  
  // Get color scheme based on dark mode
  const colorScheme = isDarkMode ? 'dark' : 'light';
  const backgroundColor = Colors[colorScheme].background;
  const textColor = Colors[colorScheme].text;
  const secondaryColor = isDarkMode ? '#9BA1A6' : '#687076';

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol name="chevron.left.forwardslash.chevron.right" size={24} color={textColor} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: textColor }]}>{t('settings.title')}</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        {/* Appearance Section */}
        <SettingsCard 
          title={t('settings.appearance')}
          isDarkMode={isDarkMode}
          textColor={textColor}
        >
          <SettingsRow
            title={t('settings.darkMode')}
            textColor={textColor}
            isDarkMode={isDarkMode}
            rightElement={
              <Switch
                value={isDarkMode}
                onValueChange={toggleDarkMode}
                trackColor={{ false: '#767577', true: Colors[colorScheme].tint }}
                thumbColor="#FFFFFF"
              />
            }
          />
        </SettingsCard>

        {/* Language Section */}
        <SettingsCard 
          title={t('settings.language')}
          isDarkMode={isDarkMode}
          textColor={textColor}
        >
          <SettingsRow
            title={t('settings.changeLanguage')}
            textColor={textColor}
            isDarkMode={isDarkMode}
            onPress={() => router.push('/language-selection')}
            rightElement={
              <Text style={{ color: secondaryColor }}>
                {language === 'en' ? 'English' : 'हिन्दी'}
              </Text>
            }
            showChevron
          />
        </SettingsCard>

        {/* Features Section */}
        <SettingsCard 
          title={t('settings.features')}
          isDarkMode={isDarkMode}
          textColor={textColor}
        >
          <SettingsRow
            title={t('settings.aiFeatures')}
            textColor={textColor}
            isDarkMode={isDarkMode}
            rightElement={
              <Switch
                value={isAIEnabled}
                onValueChange={toggleAIEnabled}
                trackColor={{ false: '#767577', true: Colors[colorScheme].tint }}
                thumbColor="#FFFFFF"
              />
            }
          />
        </SettingsCard>

        {/* About Section */}
        <SettingsCard 
          title={t('settings.about')}
          isDarkMode={isDarkMode}
          textColor={textColor}
        >
          <SettingsRow
            title={t('settings.version')}
            textColor={textColor}
            isDarkMode={isDarkMode}
            rightElement={<Text style={{ color: secondaryColor }}>1.0.0</Text>}
            showDivider
          />
          <SettingsRow
            title={t('settings.privacyPolicy')}
            textColor={textColor}
            isDarkMode={isDarkMode}
            onPress={() => {}}
            showChevron
            showDivider
          />
          <SettingsRow
            title={t('settings.termsOfService')}
            textColor={textColor}
            isDarkMode={isDarkMode}
            onPress={() => {}}
            showChevron
          />
        </SettingsCard>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    paddingTop: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    padding: 16,
    paddingBottom: 30,
  },
}); 