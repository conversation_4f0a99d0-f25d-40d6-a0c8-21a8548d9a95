import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, SafeAreaView, StyleSheet, Text, View } from 'react-native';

import { AppLogo } from '@/components/ui/AppLogo';
import { LanguageButton } from '@/components/ui/LanguageButton';
import { LoadingOverlay } from '@/components/ui/LoadingOverlay';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import { requestStoragePermissions } from '@/utils/storageUtils';

export default function LanguageSelectionScreen() {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const { setLanguage, isDarkMode } = useAppContext();
  const [isLoading, setIsLoading] = useState(false);
  
  const handleLanguageSelect = async (language: string) => {
    try {
      setIsLoading(true);
      
      // Set language
      setLanguage(language);
      i18n.changeLanguage(language);
      
      // Request permissions
      const permissionsGranted = await requestStoragePermissions();
      
      if (!permissionsGranted) {
        Alert.alert(
          "Storage Access Needed",
          "This app needs access to your media library to analyze and optimize your storage. You can grant these permissions later in the app settings.",
          [{ text: "Continue", style: "default" }]
        );
      }
      
      // Set first time setup as completed
      await AsyncStorage.setItem('hasCompletedSetup', 'true');
      await AsyncStorage.setItem('language', language);
      
      // Navigate to the main app
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Error setting up app:', error);
      Alert.alert(
        "Setup Error",
        "There was an error setting up the app. Please try again.",
        [{ text: "OK", style: "default" }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const backgroundColor = isDarkMode ? Colors.dark.background : Colors.light.background;
  const textColor = isDarkMode ? Colors.dark.text : Colors.light.text;
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      
      <View style={styles.header}>
        <AppLogo isDarkMode={isDarkMode} size="large" />
        <Text style={[styles.title, { color: textColor }]}>
          StoragePro
        </Text>
        <Text style={[styles.subtitle, { color: textColor }]}>
          Choose your preferred language
        </Text>
      </View>

      <View style={styles.languageContainer}>
        <LanguageButton 
          language="English"
          nativeText="English"
          onPress={() => handleLanguageSelect('en')}
          disabled={isLoading}
          isDarkMode={isDarkMode}
          textColor={textColor}
        />

        <LanguageButton 
          language="Hindi"
          nativeText="हिंदी"
          onPress={() => handleLanguageSelect('hi')}
          disabled={isLoading}
          isDarkMode={isDarkMode}
          textColor={textColor}
        />
      </View>
      
      {isLoading && (
        <LoadingOverlay 
          message="Setting up app..."
          isDarkMode={isDarkMode}
          textColor={textColor}
        />
      )}
      
      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: textColor }]}>
          StoragePro helps you analyze and optimize your device storage
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 60,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    textAlign: 'center',
  },
  languageContainer: {
    marginTop: 20,
  },
  footer: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    right: 20,
    alignItems: 'center',
  },
  footerText: {
    textAlign: 'center',
    fontSize: 14,
    opacity: 0.8,
  },
}); 