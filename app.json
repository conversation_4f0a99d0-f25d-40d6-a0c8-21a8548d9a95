{"expo": {"name": "StoragePro", "slug": "storage-pro", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "storagepro", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#0A7EA4"}, "edgeToEdgeEnabled": true, "package": "com.interplay_djexo.storagepro", "permissions": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#0A7EA4"}], "expo-localization", ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos to help analyze and optimize your storage.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos to your gallery when compressing images.", "isAccessMediaLocationEnabled": true}]], "experiments": {"typedRoutes": true}}}