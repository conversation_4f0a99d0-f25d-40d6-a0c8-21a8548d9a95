/**
 * Modern color palette for StoragePro - designed for elegance and accessibility
 * Inspired by modern design systems with enhanced contrast and visual hierarchy
 */

// Primary brand colors
const primaryBlue = '#0066FF';
const primaryBlueDark = '#0052CC';

// Accent colors for different storage types
const accentGreen = '#00C896';
const accentOrange = '#FF8A00';
const accentRed = '#FF3B30';
const accentPurple = '#8B5CF6';

// Neutral grays
const gray50 = '#F8FAFC';
const gray100 = '#F1F5F9';
const gray200 = '#E2E8F0';
const gray300 = '#CBD5E1';
const gray400 = '#94A3B8';
const gray500 = '#64748B';
const gray600 = '#475569';
const gray700 = '#334155';
const gray800 = '#1E293B';
const gray900 = '#0F172A';

export const Colors = {
  light: {
    text: gray900,
    textSecondary: gray600,
    background: '#FFFFFF',
    backgroundSecondary: gray50,
    tint: primaryBlue,
    tintSecondary: primaryBlueDark,
    icon: gray500,
    tabIconDefault: gray400,
    tabIconSelected: primaryBlue,
    card: '#FFFFFF',
    cardBorder: gray200,
    success: accentGreen,
    warning: accentOrange,
    error: accentRed,
    accent: accentPurple,
    shadow: 'rgba(0, 0, 0, 0.1)',
  },
  dark: {
    text: '#FFFFFF',
    textSecondary: gray300,
    background: '#000000',
    backgroundSecondary: gray900,
    tint: '#4A9EFF',
    tintSecondary: '#66B3FF',
    icon: gray400,
    tabIconDefault: gray500,
    tabIconSelected: '#4A9EFF',
    card: gray800,
    cardBorder: gray700,
    success: accentGreen,
    warning: accentOrange,
    error: accentRed,
    accent: accentPurple,
    shadow: 'rgba(0, 0, 0, 0.3)',
  },
};
