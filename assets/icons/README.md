# App Icons

This directory contains all the SVG icons used in the StoragePro app.

## Icon Requirements

All icons should:
- Be in SVG format
- Use `currentColor` for fill/stroke to support dynamic coloring
- Have a viewBox of "0 0 24 24" for consistency
- Be named according to the list below

## Required Icons

### Tab Navigation
- `home.svg` - Home tab icon
- `analytics.svg` - Analysis tab icon
- `clean.svg` - Cleaning tab icon  
- `optimize.svg` - File optimization tab icon

### UI Navigation
- `settings.svg` - Settings gear icon
- `arrow-right.svg` - Right chevron for navigation
- `back.svg` - Back navigation icon

### Analysis Screen
- `storage.svg` - Storage/archive box icon
- `duplicate.svg` - Duplicate files icon
- `calendar.svg` - Calendar/date icon
- `app.svg` - Application icon
- `ai.svg` - AI assistant icon

### Cleaning Screen
- `refresh.svg` - Refresh/reload icon
- `download.svg` - Downloaded files icon

### File Types
- `photo.svg` - Photo/image icon
- `video.svg` - Video icon
- `document.svg` - General document icon
- `text-document.svg` - Text document icon
- `music.svg` - Music/audio icon
- `spreadsheet.svg` - Spreadsheet icon
- `presentation.svg` - Presentation icon
- `folder.svg` - Folder icon

## Generating Placeholder Icons

You can generate placeholder icons for development using:

```
node scripts/generate-placeholder-icons.js
```

Replace these with proper SVG icons before production.

## Getting Icons

You can obtain appropriate SVG icons from:

1. SF Symbols (for iOS-style icons): https://developer.apple.com/sf-symbols/
2. Material Design Icons: https://material.io/resources/icons/
3. Various icon packs: https://www.flaticon.com/, https://www.iconfinder.com/, etc.

## Fallback Mechanism

If an icon is not found in this directory, the app will fall back to using Material Icons from the `@expo/vector-icons` package. However, for the best visual consistency, it's recommended to use actual SVG files for all icons.

## Adding a New Icon

1. Save the SVG file in this directory with the appropriate name
2. Make sure it's referenced correctly in `assets/icon-manifest.ts`

## Sample SVG Icon

If you need a placeholder, here's a simple example of what an SVG icon should look like:

```svg
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
  <path d="M0 0h24v24H0z" fill="none"/>
  <!-- Add your icon paths here -->
</svg>
``` 