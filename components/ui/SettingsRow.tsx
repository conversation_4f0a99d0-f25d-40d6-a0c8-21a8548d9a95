import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { IconSymbol } from './IconSymbol';

interface SettingsRowProps {
  title: string;
  textColor: string;
  isDarkMode: boolean;
  onPress?: () => void;
  rightElement?: React.ReactNode;
  showChevron?: boolean;
  showDivider?: boolean;
}

export const SettingsRow: React.FC<SettingsRowProps> = ({
  title,
  textColor,
  isDarkMode,
  onPress,
  rightElement,
  showChevron = false,
  showDivider = false,
}) => {
  const secondaryColor = isDarkMode ? '#9BA1A6' : '#687076';
  const RowComponent = onPress ? TouchableOpacity : View;

  return (
    <>
      <RowComponent style={styles.settingRow} onPress={onPress}>
        <Text style={[styles.settingText, { color: textColor }]}>{title}</Text>
        {rightElement ? (
          <View style={styles.settingAction}>
            {rightElement}
            {showChevron && (
              <IconSymbol name="chevron.right" size={20} color={secondaryColor} />
            )}
          </View>
        ) : showChevron ? (
          <IconSymbol name="chevron.right" size={20} color={secondaryColor} />
        ) : null}
      </RowComponent>
      {showDivider && <View style={[styles.divider, { opacity: isDarkMode ? 0.2 : 0.1 }]} />}
    </>
  );
};

const styles = StyleSheet.create({
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  settingText: {
    fontSize: 16,
  },
  settingAction: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  divider: {
    height: 1,
    backgroundColor: '#333333',
  },
}); 