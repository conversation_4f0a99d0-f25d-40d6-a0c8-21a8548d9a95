import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Dimensions, ScrollView } from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { useTranslation } from 'react-i18next';

import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import { StorageInfo, StorageTrend, formatBytes, getStorageTrends } from '@/utils/storageUtils';

interface StorageAnalyticsProps {
  storageInfo: StorageInfo;
}

const screenWidth = Dimensions.get('window').width;

export const StorageAnalytics: React.FC<StorageAnalyticsProps> = ({ storageInfo }) => {
  const { t } = useTranslation();
  const { isDarkMode } = useAppContext();
  const [trends, setTrends] = useState<StorageTrend[]>([]);
  const [loading, setLoading] = useState(true);

  const colorScheme = isDarkMode ? 'dark' : 'light';
  const textColor = Colors[colorScheme].text;
  const backgroundColor = Colors[colorScheme].background;
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';

  useEffect(() => {
    const loadTrends = async () => {
      try {
        const trendData = await getStorageTrends();
        setTrends(trendData);
      } catch (error) {
        console.error('Error loading trends:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTrends();
  }, []);

  // Prepare pie chart data for storage breakdown
  const pieChartData = storageInfo.breakdown ? [
    {
      name: t('analytics.images'),
      population: storageInfo.breakdown.images,
      color: '#FF6B6B',
      legendFontColor: textColor,
      legendFontSize: 12,
    },
    {
      name: t('analytics.videos'),
      population: storageInfo.breakdown.videos,
      color: '#4ECDC4',
      legendFontColor: textColor,
      legendFontSize: 12,
    },
    {
      name: t('analytics.audio'),
      population: storageInfo.breakdown.audio,
      color: '#45B7D1',
      legendFontColor: textColor,
      legendFontSize: 12,
    },
    {
      name: t('analytics.documents'),
      population: storageInfo.breakdown.documents,
      color: '#FFA07A',
      legendFontColor: textColor,
      legendFontSize: 12,
    },
    {
      name: t('analytics.apps'),
      population: storageInfo.breakdown.apps,
      color: '#98D8C8',
      legendFontColor: textColor,
      legendFontSize: 12,
    },
    {
      name: t('analytics.system'),
      population: storageInfo.breakdown.system,
      color: '#F7DC6F',
      legendFontColor: textColor,
      legendFontSize: 12,
    },
    {
      name: t('analytics.other'),
      population: storageInfo.breakdown.other,
      color: '#BB8FCE',
      legendFontColor: textColor,
      legendFontSize: 12,
    },
  ].filter(item => item.population > 0) : [];

  // Prepare line chart data for trends
  const lineChartData = {
    labels: trends.slice(-7).map(trend => {
      const date = new Date(trend.date);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    }),
    datasets: [
      {
        data: trends.slice(-7).map(trend => trend.usedSpace / (1024 * 1024 * 1024)), // Convert to GB
        color: (opacity = 1) => `rgba(74, 144, 226, ${opacity})`,
        strokeWidth: 3,
      },
    ],
  };

  const chartConfig = {
    backgroundColor: cardColor,
    backgroundGradientFrom: cardColor,
    backgroundGradientTo: cardColor,
    decimalPlaces: 1,
    color: (opacity = 1) => `rgba(74, 144, 226, ${opacity})`,
    labelColor: (opacity = 1) => isDarkMode ? `rgba(255, 255, 255, ${opacity})` : `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '4',
      strokeWidth: '2',
      stroke: '#4A90E2',
    },
  };

  return (
    <ScrollView style={styles.container}>
      {/* Storage Breakdown Pie Chart */}
      <View style={[styles.chartContainer, { backgroundColor: cardColor }]}>
        <Text style={[styles.chartTitle, { color: textColor }]}>
          {t('analytics.storageBreakdown')}
        </Text>
        {pieChartData.length > 0 && (
          <PieChart
            data={pieChartData}
            width={screenWidth - 60}
            height={220}
            chartConfig={chartConfig}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
            center={[10, 10]}
            absolute
          />
        )}
      </View>

      {/* Storage Trends Line Chart */}
      <View style={[styles.chartContainer, { backgroundColor: cardColor }]}>
        <Text style={[styles.chartTitle, { color: textColor }]}>
          {t('analytics.usageTrends')} (GB)
        </Text>
        {trends.length > 0 && (
          <LineChart
            data={lineChartData}
            width={screenWidth - 60}
            height={220}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
          />
        )}
      </View>

      {/* Storage Statistics */}
      <View style={[styles.statsContainer, { backgroundColor: cardColor }]}>
        <Text style={[styles.chartTitle, { color: textColor }]}>
          {t('analytics.storageStats')}
        </Text>
        
        <View style={styles.statRow}>
          <Text style={[styles.statLabel, { color: textColor }]}>
            {t('analytics.totalCapacity')}:
          </Text>
          <Text style={[styles.statValue, { color: textColor }]}>
            {formatBytes(storageInfo.totalSpace)}
          </Text>
        </View>

        <View style={styles.statRow}>
          <Text style={[styles.statLabel, { color: textColor }]}>
            {t('analytics.usedSpace')}:
          </Text>
          <Text style={[styles.statValue, { color: textColor }]}>
            {formatBytes(storageInfo.usedSpace)} ({((storageInfo.usedSpace / storageInfo.totalSpace) * 100).toFixed(1)}%)
          </Text>
        </View>

        <View style={styles.statRow}>
          <Text style={[styles.statLabel, { color: textColor }]}>
            {t('analytics.freeSpace')}:
          </Text>
          <Text style={[styles.statValue, { color: textColor }]}>
            {formatBytes(storageInfo.freeSpace)} ({((storageInfo.freeSpace / storageInfo.totalSpace) * 100).toFixed(1)}%)
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  chartContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  statsContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  statLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
  },
});
