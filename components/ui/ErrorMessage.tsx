import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { useAppContext } from '@/context/AppContext';
import { IconSymbol } from './IconSymbol';

interface ErrorMessageProps {
  message?: string;
  onDismiss?: () => void;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  onDismiss,
}) => {
  const { error, clearError } = useAppContext();
  const errorMessage = message || error;
  
  if (!errorMessage) return null;
  
  const handleDismiss = () => {
    if (onDismiss) {
      onDismiss();
    } else if (error) {
      clearError();
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <IconSymbol name="exclamationmark.triangle.fill" size={24} color="#FF3B30" />
        <Text style={styles.message}>{errorMessage}</Text>
      </View>
      <TouchableOpacity onPress={handleDismiss} style={styles.dismissButton}>
        <IconSymbol name="xmark" size={20} color="#666" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFEBE9',
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderLeftWidth: 4,
    borderLeftColor: '#FF3B30',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  message: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  dismissButton: {
    padding: 4,
  },
}); 