import React from 'react';
import { StyleSheet, View, ViewProps } from 'react-native';
import { BlurView } from 'expo-blur';
import { Colors } from '@/constants/Colors';

interface GlassCardProps extends ViewProps {
  children: React.ReactNode;
  isDarkMode: boolean;
  intensity?: number;
  tint?: 'light' | 'dark' | 'default';
  borderRadius?: number;
  padding?: number;
}

export const GlassCard: React.FC<GlassCardProps> = ({
  children,
  isDarkMode,
  intensity = 20,
  tint = 'default',
  borderRadius = 16,
  padding = 16,
  style,
  ...props
}) => {
  const colorScheme = isDarkMode ? 'dark' : 'light';
  
  return (
    <View 
      style={[
        styles.container,
        {
          borderRadius,
          backgroundColor: isDarkMode 
            ? 'rgba(30, 30, 30, 0.8)' 
            : 'rgba(255, 255, 255, 0.9)',
          borderColor: Colors[colorScheme].cardBorder,
          shadowColor: Colors[colorScheme].shadow,
        },
        style
      ]}
      {...props}
    >
      <BlurView
        intensity={intensity}
        tint={tint}
        style={[
          styles.blurContainer,
          {
            borderRadius,
            padding,
          }
        ]}
      >
        {children}
      </BlurView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    overflow: 'hidden',
  },
  blurContainer: {
    flex: 1,
  },
});
