import { Colors } from '@/constants/Colors';
import React, { useEffect } from 'react';
import { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import Animated, {
    interpolateColor,
    useAnimatedStyle,
    useSharedValue,
    withSpring,
    withTiming,
} from 'react-native-reanimated';

interface ProgressBarProps {
  progress: number; // Value from 0 to 1
  progressColor?: string;
  trackColor?: string;
  style?: StyleProp<ViewStyle>;
  height?: number;
  animated?: boolean;
  showGradient?: boolean;
  isDarkMode?: boolean;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  progressColor,
  trackColor,
  style,
  height = 8,
  animated = true,
  showGradient = true,
  isDarkMode = false,
}) => {
  // Ensure progress is between 0 and 1
  const progressValue = Math.min(Math.max(progress, 0), 1);

  // Animated values
  const animatedProgress = useSharedValue(0);
  const animatedColor = useSharedValue(0);

  // Default colors based on theme
  const defaultProgressColor = progressColor || (isDarkMode ? Colors.dark.tint : Colors.light.tint);
  const defaultTrackColor = trackColor || (isDarkMode ? Colors.dark.cardBorder : Colors.light.cardBorder);

  // Update animated values when progress changes
  useEffect(() => {
    if (animated) {
      animatedProgress.value = withSpring(progressValue, {
        damping: 15,
        stiffness: 150,
      });

      // Color animation based on progress (green -> yellow -> red)
      animatedColor.value = withTiming(progressValue, { duration: 300 });
    } else {
      animatedProgress.value = progressValue;
      animatedColor.value = progressValue;
    }
  }, [progressValue, animated]);

  // Animated styles
  const animatedProgressStyle = useAnimatedStyle(() => {
    const width = `${animatedProgress.value * 100}%`;

    let backgroundColor = defaultProgressColor;

    if (showGradient) {
      // Dynamic color based on progress
      backgroundColor = interpolateColor(
        animatedColor.value,
        [0, 0.5, 0.8, 1],
        [Colors.light.success, Colors.light.warning, Colors.light.warning, Colors.light.error]
      );
    }

    return {
      width,
      backgroundColor,
    };
  });

  return (
    <View style={[styles.track, { backgroundColor: defaultTrackColor, height }, style]}>
      <Animated.View
        style={[
          styles.progress,
          { height },
          animatedProgressStyle,
        ]}
      />
      {/* Shimmer effect for loading states */}
      {progressValue > 0 && (
        <View style={[styles.shimmer, { height }]} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  track: {
    borderRadius: 6,
    overflow: 'hidden',
    width: '100%',
    position: 'relative',
  },
  progress: {
    borderRadius: 6,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 6,
  },
});