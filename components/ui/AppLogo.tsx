import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

interface AppLogoProps {
  size?: 'small' | 'medium' | 'large';
  isDarkMode: boolean;
}

export const AppLogo: React.FC<AppLogoProps> = ({
  size = 'large',
  isDarkMode,
}) => {
  // Determine size dimensions
  const dimensions = {
    small: { width: 60, height: 60, fontSize: 24 },
    medium: { width: 80, height: 80, fontSize: 32 },
    large: { width: 120, height: 120, fontSize: 48 },
  };

  const { width, height, fontSize } = dimensions[size];

  return (
    <View 
      style={[
        styles.logoPlaceholder,
        { width, height, borderRadius: width / 4 }
      ]}
    >
      <Text style={[styles.logoText, { fontSize, color: isDarkMode ? '#fff' : '#000' }]}>SP</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  logoPlaceholder: {
    backgroundColor: '#0A7EA4',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  logoText: {
    fontWeight: 'bold',
  },
}); 