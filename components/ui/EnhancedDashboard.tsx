import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';

import { IconSymbol } from './IconSymbol';
import { StorageRing } from './StorageRing';
import { Colors } from '@/constants/Colors';
import { useAppContext } from '@/context/AppContext';
import { StorageInfo, formatBytes } from '@/utils/storageUtils';
import { PerformanceMonitor } from '@/utils/performanceMonitor';
import { SecurityManager } from '@/utils/securityManager';
import { CloudBackupManager } from '@/utils/cloudBackup';

interface EnhancedDashboardProps {
  storageInfo: StorageInfo;
  onQuickAction: (action: string) => void;
}

const screenWidth = Dimensions.get('window').width;

export const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({
  storageInfo,
  onQuickAction,
}) => {
  const { t } = useTranslation();
  const { isDarkMode, isAIEnabled } = useAppContext();
  const [performanceScore, setPerformanceScore] = useState(0);
  const [securityScore, setSecurityScore] = useState(0);
  const [backupStatus, setBackupStatus] = useState<'none' | 'partial' | 'complete'>('none');
  const [alerts, setAlerts] = useState<any[]>([]);

  const colorScheme = isDarkMode ? 'dark' : 'light';
  const backgroundColor = Colors[colorScheme].background;
  const textColor = Colors[colorScheme].text;
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';
  const accentColor = Colors[colorScheme].tint;

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Load performance data
      const performanceMonitor = PerformanceMonitor.getInstance();
      const perfScore = performanceMonitor.getPerformanceScore();
      setPerformanceScore(perfScore);

      // Load security data
      const securityManager = SecurityManager.getInstance();
      const securityReport = securityManager.generateSecurityReport();
      setSecurityScore(securityReport.overallScore);
      setAlerts(securityReport.recentAlerts.slice(0, 3)); // Top 3 alerts

      // Load backup status
      const backupManager = CloudBackupManager.getInstance();
      const providers = backupManager.getProviders();
      const connectedProviders = providers.filter(p => p.isConnected);
      
      if (connectedProviders.length === 0) {
        setBackupStatus('none');
      } else if (connectedProviders.length < providers.length) {
        setBackupStatus('partial');
      } else {
        setBackupStatus('complete');
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const getStorageHealthColor = () => {
    const usagePercentage = (storageInfo.usedSpace / storageInfo.totalSpace) * 100;
    if (usagePercentage > 90) return '#FF3B30';
    if (usagePercentage > 80) return '#FF9500';
    if (usagePercentage > 70) return '#FFCC00';
    return '#34C759';
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#34C759';
    if (score >= 60) return '#FFCC00';
    if (score >= 40) return '#FF9500';
    return '#FF3B30';
  };

  const QuickActionCard = ({ 
    title, 
    icon, 
    action, 
    color = accentColor,
    subtitle 
  }: { 
    title: string; 
    icon: string; 
    action: string; 
    color?: string;
    subtitle?: string;
  }) => (
    <TouchableOpacity
      style={[styles.quickActionCard, { backgroundColor: cardColor }]}
      onPress={() => onQuickAction(action)}
    >
      <View style={[styles.quickActionIcon, { backgroundColor: color + '20' }]}>
        <IconSymbol name={icon} size={24} color={color} />
      </View>
      <Text style={[styles.quickActionTitle, { color: textColor }]}>{title}</Text>
      {subtitle && (
        <Text style={[styles.quickActionSubtitle, { color: textColor + '80' }]}>
          {subtitle}
        </Text>
      )}
    </TouchableOpacity>
  );

  const StatusCard = ({ 
    title, 
    value, 
    icon, 
    color, 
    onPress 
  }: { 
    title: string; 
    value: string | number; 
    icon: string; 
    color: string;
    onPress?: () => void;
  }) => (
    <TouchableOpacity
      style={[styles.statusCard, { backgroundColor: cardColor }]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.statusCardHeader}>
        <IconSymbol name={icon} size={20} color={color} />
        <Text style={[styles.statusCardTitle, { color: textColor }]}>{title}</Text>
      </View>
      <Text style={[styles.statusCardValue, { color }]}>{value}</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      {/* Storage Overview */}
      <View style={[styles.storageOverview, { backgroundColor: cardColor }]}>
        <View style={styles.storageHeader}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            {t('dashboard.storageOverview')}
          </Text>
          <TouchableOpacity onPress={() => onQuickAction('analyze')}>
            <IconSymbol name="chart.pie.fill" size={24} color={accentColor} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.storageContent}>
          <StorageRing
            totalSpace={storageInfo.totalSpace}
            usedSpace={storageInfo.usedSpace}
            size={120}
          />
          <View style={styles.storageDetails}>
            <View style={styles.storageDetailRow}>
              <Text style={[styles.storageLabel, { color: textColor }]}>
                {t('dashboard.used')}:
              </Text>
              <Text style={[styles.storageValue, { color: getStorageHealthColor() }]}>
                {formatBytes(storageInfo.usedSpace)}
              </Text>
            </View>
            <View style={styles.storageDetailRow}>
              <Text style={[styles.storageLabel, { color: textColor }]}>
                {t('dashboard.free')}:
              </Text>
              <Text style={[styles.storageValue, { color: textColor }]}>
                {formatBytes(storageInfo.freeSpace)}
              </Text>
            </View>
            <View style={styles.storageDetailRow}>
              <Text style={[styles.storageLabel, { color: textColor }]}>
                {t('dashboard.total')}:
              </Text>
              <Text style={[styles.storageValue, { color: textColor }]}>
                {formatBytes(storageInfo.totalSpace)}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* System Health Status */}
      <View style={styles.statusGrid}>
        <StatusCard
          title={t('dashboard.performance')}
          value={`${performanceScore}%`}
          icon="speedometer"
          color={getScoreColor(performanceScore)}
          onPress={() => onQuickAction('performance')}
        />
        <StatusCard
          title={t('dashboard.security')}
          value={`${securityScore}%`}
          icon="shield.fill"
          color={getScoreColor(securityScore)}
          onPress={() => onQuickAction('security')}
        />
        <StatusCard
          title={t('dashboard.backup')}
          value={backupStatus === 'complete' ? t('dashboard.complete') : 
                 backupStatus === 'partial' ? t('dashboard.partial') : 
                 t('dashboard.none')}
          icon="cloud.fill"
          color={backupStatus === 'complete' ? '#34C759' : 
                 backupStatus === 'partial' ? '#FFCC00' : '#FF3B30'}
          onPress={() => onQuickAction('backup')}
        />
      </View>

      {/* Quick Actions */}
      <View style={[styles.section, { backgroundColor: cardColor }]}>
        <Text style={[styles.sectionTitle, { color: textColor }]}>
          {t('dashboard.quickActions')}
        </Text>
        <View style={styles.quickActionsGrid}>
          <QuickActionCard
            title={t('dashboard.smartClean')}
            icon="wand.and.stars"
            action="smart-clean"
            color="#FF6B6B"
            subtitle={t('dashboard.aiPowered')}
          />
          <QuickActionCard
            title={t('dashboard.compress')}
            icon="arrow.down.to.line"
            action="compress"
            color="#4ECDC4"
            subtitle={t('dashboard.saveSpace')}
          />
          <QuickActionCard
            title={t('dashboard.organize')}
            icon="folder.fill"
            action="organize"
            color="#45B7D1"
            subtitle={t('dashboard.autoSort')}
          />
          <QuickActionCard
            title={t('dashboard.backup')}
            icon="cloud.fill"
            action="backup"
            color="#96CEB4"
            subtitle={t('dashboard.cloudSync')}
          />
        </View>
      </View>

      {/* Recent Alerts */}
      {alerts.length > 0 && (
        <View style={[styles.section, { backgroundColor: cardColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            {t('dashboard.recentAlerts')}
          </Text>
          {alerts.map((alert, index) => (
            <TouchableOpacity
              key={alert.id}
              style={[styles.alertItem, { borderLeftColor: getScoreColor(alert.severity === 'high' ? 20 : 60) }]}
              onPress={() => onQuickAction('security')}
            >
              <View style={styles.alertContent}>
                <Text style={[styles.alertTitle, { color: textColor }]}>{alert.title}</Text>
                <Text style={[styles.alertDescription, { color: textColor + '80' }]}>
                  {alert.description}
                </Text>
              </View>
              <IconSymbol name="chevron.right" size={16} color={textColor + '60'} />
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* AI Insights */}
      {isAIEnabled && (
        <View style={[styles.section, { backgroundColor: cardColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            {t('dashboard.aiInsights')}
          </Text>
          <View style={styles.insightCard}>
            <IconSymbol name="lightbulb.fill" size={24} color="#FFD700" />
            <View style={styles.insightContent}>
              <Text style={[styles.insightTitle, { color: textColor }]}>
                {t('dashboard.optimizationTip')}
              </Text>
              <Text style={[styles.insightDescription, { color: textColor + '80' }]}>
                {t('dashboard.aiSuggestion')}
              </Text>
            </View>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  storageOverview: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  storageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  storageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  storageDetails: {
    flex: 1,
    marginLeft: 20,
  },
  storageDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  storageLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  storageValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  statusGrid: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 16,
    gap: 12,
  },
  statusCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statusCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusCardTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 6,
  },
  statusCardValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  section: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    width: (screenWidth - 64) / 2,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  quickActionSubtitle: {
    fontSize: 12,
    textAlign: 'center',
  },
  alertItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderLeftWidth: 4,
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  alertContent: {
    flex: 1,
  },
  alertTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  alertDescription: {
    fontSize: 12,
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
  },
  insightContent: {
    flex: 1,
    marginLeft: 12,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  insightDescription: {
    fontSize: 12,
  },
});
