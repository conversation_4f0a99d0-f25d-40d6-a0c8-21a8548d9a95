import React, { useEffect } from 'react';
import { StyleSheet, View, Text } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import Svg, { Circle, Defs, LinearGradient, Stop } from 'react-native-svg';
import { Colors } from '@/constants/Colors';

interface StorageRingProps {
  percentage: number;
  size?: number;
  strokeWidth?: number;
  isDarkMode?: boolean;
  showPercentage?: boolean;
  centerContent?: React.ReactNode;
}

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

export const StorageRing: React.FC<StorageRingProps> = ({
  percentage,
  size = 120,
  strokeWidth = 8,
  isDarkMode = false,
  showPercentage = true,
  centerContent,
}) => {
  const animatedValue = useSharedValue(0);
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  
  const colorScheme = isDarkMode ? 'dark' : 'light';
  
  useEffect(() => {
    animatedValue.value = withTiming(percentage / 100, {
      duration: 1500,
    });
  }, [percentage]);

  const animatedProps = useAnimatedStyle(() => {
    const strokeDashoffset = interpolate(
      animatedValue.value,
      [0, 1],
      [circumference, 0]
    );

    return {
      strokeDashoffset,
    };
  });

  // Dynamic color based on usage
  const getStrokeColor = () => {
    if (percentage < 50) return Colors[colorScheme].success;
    if (percentage < 80) return Colors[colorScheme].warning;
    return Colors[colorScheme].error;
  };

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Svg width={size} height={size} style={styles.svg}>
        <Defs>
          <LinearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={getStrokeColor()} stopOpacity="1" />
            <Stop offset="100%" stopColor={getStrokeColor()} stopOpacity="0.6" />
          </LinearGradient>
        </Defs>
        
        {/* Background circle */}
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={Colors[colorScheme].cardBorder}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        
        {/* Progress circle */}
        <AnimatedCircle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="url(#gradient)"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={circumference}
          strokeDashoffset={circumference}
          strokeLinecap="round"
          style={animatedProps}
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
      </Svg>
      
      {/* Center content */}
      <View style={styles.centerContent}>
        {centerContent || (showPercentage && (
          <Text style={[
            styles.percentageText,
            { color: Colors[colorScheme].text }
          ]}>
            {Math.round(percentage)}%
          </Text>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  svg: {
    position: 'absolute',
  },
  centerContent: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  percentageText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
});
