// Fallback for using MaterialIcons on Android and web.

import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { StyleProp, TextStyle } from 'react-native';

interface IconSymbolProps {
  name: string;
  size?: number;
  color?: string;
  style?: StyleProp<TextStyle>;
}

export const IconSymbol: React.FC<IconSymbolProps> = ({ 
  name, 
  size = 24, 
  color, 
  style 
}) => {
  // Map from our custom names to Material Icons names
  const materialIconMap: { [key: string]: keyof typeof MaterialIcons.glyphMap } = {
    'house.fill': 'home',
    'chart.pie.fill': 'pie-chart',
    'trash.fill': 'delete',
    'arrow.down.to.line': 'arrow-downward',
    'gear': 'settings',
    'chevron.right': 'chevron-right',
    'chevron.left.forwardslash.chevron.right': 'compare-arrows',
    'archivebox.fill': 'archive',
    'doc.on.doc.fill': 'file-copy',
    'calendar': 'calendar-today',
    'app.fill': 'apps',
    'wand.and.stars': 'auto-fix-high',
    'arrow.triangle.2.circlepath': 'refresh',
    'arrow.down.circle.fill': 'get-app',
    'photo.fill': 'photo',
    'video.fill': 'videocam',
    'doc.fill': 'description',
    'doc.text.fill': 'article',
    'music.note': 'music-note',
    'tablecells.fill': 'grid-on',
    'chart.bar.fill': 'bar-chart',
    'folder.fill': 'folder',
  };

  // Use a type assertion to handle the fallback case
  const materialName = materialIconMap[name] || 'help-outline';
  
  return (
    <MaterialIcons 
      name={materialName as keyof typeof MaterialIcons.glyphMap} 
      size={size} 
      color={color} 
      style={style}
    />
  );
};
