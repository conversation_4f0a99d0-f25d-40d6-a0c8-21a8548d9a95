import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

interface SettingsCardProps {
  title: string;
  children: React.ReactNode;
  isDarkMode: boolean;
  textColor: string;
}

export const SettingsCard: React.FC<SettingsCardProps> = ({
  title,
  children,
  isDarkMode,
  textColor,
}) => {
  const cardColor = isDarkMode ? '#1E1E1E' : '#FFFFFF';

  return (
    <>
      <Text style={[styles.sectionTitle, { color: textColor }]}>{title}</Text>
      <View style={[styles.card, { backgroundColor: cardColor }]}>
        {children}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 16,
  },
  card: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
}); 