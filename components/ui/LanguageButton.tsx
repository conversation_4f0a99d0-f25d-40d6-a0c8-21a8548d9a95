import React from 'react';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';

interface LanguageButtonProps {
  language: string;
  nativeText: string;
  onPress: () => void;
  disabled?: boolean;
  isDarkMode: boolean;
  textColor: string;
}

export const LanguageButton: React.FC<LanguageButtonProps> = ({
  language,
  nativeText,
  onPress,
  disabled = false,
  isDarkMode,
  textColor,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.languageButton,
        { backgroundColor: isDarkMode ? '#333' : '#f0f0f0' }
      ]}
      onPress={onPress}
      disabled={disabled}
    >
      <Text style={[styles.languageText, { color: textColor }]}>
        {language}
      </Text>
      <Text style={[styles.nativeText, { color: textColor }]}>
        {nativeText}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  languageButton: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  languageText: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 4,
  },
  nativeText: {
    fontSize: 16,
  },
}); 