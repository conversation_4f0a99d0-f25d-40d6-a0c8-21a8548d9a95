{"name": "create_storage", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "generate-icons": "node ./scripts/generate-placeholder-icons.js"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "expo": "~53.0.9", "expo-blur": "~14.1.4", "expo-brightness": "~13.1.4", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.4", "expo-dev-client": "~5.1.8", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-gl": "~15.1.5", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-manipulator": "~13.1.7", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-local-authentication": "~15.1.4", "expo-localization": "~16.1.5", "expo-media-library": "~17.1.6", "expo-notifications": "~0.30.8", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-task-manager": "~12.1.4", "expo-web-browser": "~14.1.6", "i18next": "^25.2.1", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-modal": "^13.0.1", "react-native-progress": "^5.0.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.9.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}