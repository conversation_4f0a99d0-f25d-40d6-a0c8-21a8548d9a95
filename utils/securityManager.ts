import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';
import * as LocalAuthentication from 'expo-local-authentication';
import { FileInfo } from './storageUtils';

export interface SecuritySettings {
  biometricEnabled: boolean;
  pinEnabled: boolean;
  autoLockEnabled: boolean;
  autoLockTimeout: number; // minutes
  secureDeleteEnabled: boolean;
  encryptBackups: boolean;
  requireAuthForSensitiveActions: boolean;
}

export interface SecurityAlert {
  id: string;
  type: 'suspicious_activity' | 'permission_change' | 'large_deletion' | 'security_breach';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  timestamp: number;
  acknowledged: boolean;
}

export interface FileSecurityInfo {
  isSystemFile: boolean;
  isProtected: boolean;
  riskLevel: 'low' | 'medium' | 'high';
  warnings: string[];
}

export class SecurityManager {
  private static instance: SecurityManager;
  private settings: SecuritySettings;
  private alerts: SecurityAlert[] = [];
  private lastActivity: number = Date.now();
  private isLocked: boolean = false;

  private constructor() {
    this.loadSettings();
    this.loadAlerts();
    this.startActivityMonitoring();
  }

  static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  /**
   * Load security settings
   */
  private async loadSettings(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('security_settings');
      this.settings = stored ? JSON.parse(stored) : {
        biometricEnabled: false,
        pinEnabled: false,
        autoLockEnabled: false,
        autoLockTimeout: 5,
        secureDeleteEnabled: true,
        encryptBackups: true,
        requireAuthForSensitiveActions: true,
      };
    } catch (error) {
      console.error('Error loading security settings:', error);
      this.settings = {
        biometricEnabled: false,
        pinEnabled: false,
        autoLockEnabled: false,
        autoLockTimeout: 5,
        secureDeleteEnabled: true,
        encryptBackups: true,
        requireAuthForSensitiveActions: true,
      };
    }
  }

  /**
   * Save security settings
   */
  async saveSettings(settings: Partial<SecuritySettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    try {
      await AsyncStorage.setItem('security_settings', JSON.stringify(this.settings));
    } catch (error) {
      console.error('Error saving security settings:', error);
    }
  }

  /**
   * Check if biometric authentication is available
   */
  async isBiometricAvailable(): Promise<boolean> {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      return hasHardware && isEnrolled;
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return false;
    }
  }

  /**
   * Authenticate user with biometrics or PIN
   */
  async authenticate(reason: string = 'Please authenticate to continue'): Promise<boolean> {
    try {
      if (this.settings.biometricEnabled && await this.isBiometricAvailable()) {
        const result = await LocalAuthentication.authenticateAsync({
          promptMessage: reason,
          fallbackLabel: 'Use PIN',
        });
        return result.success;
      }
      
      // Fallback to PIN or other authentication method
      // In a real app, you'd implement PIN verification here
      return true; // Placeholder
    } catch (error) {
      console.error('Authentication error:', error);
      return false;
    }
  }

  /**
   * Analyze file security
   */
  analyzeFileSecurity(file: FileInfo): FileSecurityInfo {
    const warnings: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    let isSystemFile = false;
    let isProtected = false;

    // Check if it's a system file
    const systemPaths = ['/system/', '/android_asset/', '/data/system/', 'System Volume Information'];
    const systemExtensions = ['.sys', '.dll', '.exe', '.bat', '.cmd'];
    
    if (systemPaths.some(path => file.path.includes(path)) ||
        systemExtensions.some(ext => file.name.toLowerCase().endsWith(ext))) {
      isSystemFile = true;
      isProtected = true;
      riskLevel = 'high';
      warnings.push('This is a system file. Deleting it may cause system instability.');
    }

    // Check for large files
    if (file.size > 1024 * 1024 * 1024) { // > 1GB
      riskLevel = 'medium';
      warnings.push('This is a large file. Make sure you have a backup before deleting.');
    }

    // Check for important file types
    const importantExtensions = ['.doc', '.docx', '.pdf', '.xls', '.xlsx', '.ppt', '.pptx'];
    if (importantExtensions.some(ext => file.name.toLowerCase().endsWith(ext))) {
      riskLevel = 'medium';
      warnings.push('This appears to be an important document. Consider backing it up.');
    }

    // Check for media files with metadata
    if (file.type === 'image' || file.type === 'video') {
      warnings.push('Media files may contain personal metadata (location, date, etc.).');
    }

    return {
      isSystemFile,
      isProtected,
      riskLevel,
      warnings,
    };
  }

  /**
   * Validate deletion request
   */
  async validateDeletion(files: FileInfo[]): Promise<{
    allowed: boolean;
    warnings: string[];
    requiresAuth: boolean;
  }> {
    const warnings: string[] = [];
    let requiresAuth = false;
    let allowed = true;

    for (const file of files) {
      const security = this.analyzeFileSecurity(file);
      
      if (security.isSystemFile) {
        allowed = false;
        warnings.push(`Cannot delete system file: ${file.name}`);
      }
      
      if (security.riskLevel === 'high') {
        requiresAuth = true;
        warnings.push(`High-risk file detected: ${file.name}`);
      }
      
      warnings.push(...security.warnings);
    }

    // Check for bulk deletion
    if (files.length > 50) {
      requiresAuth = true;
      warnings.push('Bulk deletion detected. Please confirm this action.');
    }

    // Check total size
    const totalSize = files.reduce((sum, f) => sum + f.size, 0);
    if (totalSize > 5 * 1024 * 1024 * 1024) { // > 5GB
      requiresAuth = true;
      warnings.push('Large amount of data will be deleted. Please confirm.');
    }

    return { allowed, warnings, requiresAuth };
  }

  /**
   * Create security alert
   */
  createAlert(
    type: SecurityAlert['type'],
    severity: SecurityAlert['severity'],
    title: string,
    description: string
  ): void {
    const alert: SecurityAlert = {
      id: `alert_${Date.now()}`,
      type,
      severity,
      title,
      description,
      timestamp: Date.now(),
      acknowledged: false,
    };

    this.alerts.unshift(alert);
    
    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(0, 100);
    }

    this.saveAlerts();
  }

  /**
   * Get security alerts
   */
  getAlerts(): SecurityAlert[] {
    return this.alerts;
  }

  /**
   * Acknowledge alert
   */
  acknowledgeAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.saveAlerts();
    }
  }

  /**
   * Clear acknowledged alerts
   */
  clearAcknowledgedAlerts(): void {
    this.alerts = this.alerts.filter(a => !a.acknowledged);
    this.saveAlerts();
  }

  /**
   * Monitor suspicious activity
   */
  monitorActivity(action: string, details: any): void {
    this.lastActivity = Date.now();

    // Check for suspicious patterns
    if (action === 'bulk_delete' && details.count > 100) {
      this.createAlert(
        'suspicious_activity',
        'medium',
        'Large Bulk Deletion',
        `Attempted to delete ${details.count} files at once`
      );
    }

    if (action === 'permission_change') {
      this.createAlert(
        'permission_change',
        'low',
        'Permission Changed',
        `App permissions were modified: ${details.permission}`
      );
    }

    if (action === 'large_deletion' && details.size > 10 * 1024 * 1024 * 1024) {
      this.createAlert(
        'large_deletion',
        'high',
        'Large Data Deletion',
        `Deleted ${(details.size / (1024 * 1024 * 1024)).toFixed(2)} GB of data`
      );
    }
  }

  /**
   * Start activity monitoring for auto-lock
   */
  private startActivityMonitoring(): void {
    if (this.settings.autoLockEnabled) {
      setInterval(() => {
        const inactiveTime = Date.now() - this.lastActivity;
        const lockTimeout = this.settings.autoLockTimeout * 60 * 1000; // Convert to ms
        
        if (inactiveTime > lockTimeout && !this.isLocked) {
          this.lockApp();
        }
      }, 30000); // Check every 30 seconds
    }
  }

  /**
   * Lock the app
   */
  lockApp(): void {
    this.isLocked = true;
    // In a real app, this would trigger the lock screen
    console.log('App locked due to inactivity');
  }

  /**
   * Unlock the app
   */
  async unlockApp(): Promise<boolean> {
    if (this.settings.requireAuthForSensitiveActions) {
      const authenticated = await this.authenticate('Please authenticate to unlock the app');
      if (authenticated) {
        this.isLocked = false;
        this.lastActivity = Date.now();
        return true;
      }
      return false;
    }
    
    this.isLocked = false;
    this.lastActivity = Date.now();
    return true;
  }

  /**
   * Check if app is locked
   */
  isAppLocked(): boolean {
    return this.isLocked;
  }

  /**
   * Get security settings
   */
  getSettings(): SecuritySettings {
    return this.settings;
  }

  /**
   * Generate security report
   */
  generateSecurityReport(): {
    overallScore: number;
    recommendations: string[];
    recentAlerts: SecurityAlert[];
    securityLevel: 'low' | 'medium' | 'high';
  } {
    let score = 100;
    const recommendations: string[] = [];

    // Check security settings
    if (!this.settings.biometricEnabled && !this.settings.pinEnabled) {
      score -= 30;
      recommendations.push('Enable biometric or PIN authentication');
    }

    if (!this.settings.autoLockEnabled) {
      score -= 20;
      recommendations.push('Enable auto-lock for better security');
    }

    if (!this.settings.secureDeleteEnabled) {
      score -= 15;
      recommendations.push('Enable secure deletion to prevent data recovery');
    }

    if (!this.settings.encryptBackups) {
      score -= 15;
      recommendations.push('Enable backup encryption');
    }

    // Check recent alerts
    const recentAlerts = this.alerts.filter(a => 
      Date.now() - a.timestamp < 7 * 24 * 60 * 60 * 1000 // Last 7 days
    );

    const criticalAlerts = recentAlerts.filter(a => a.severity === 'critical').length;
    const highAlerts = recentAlerts.filter(a => a.severity === 'high').length;

    score -= criticalAlerts * 20;
    score -= highAlerts * 10;

    score = Math.max(0, Math.min(100, score));

    let securityLevel: 'low' | 'medium' | 'high';
    if (score >= 80) securityLevel = 'high';
    else if (score >= 60) securityLevel = 'medium';
    else securityLevel = 'low';

    return {
      overallScore: score,
      recommendations,
      recentAlerts,
      securityLevel,
    };
  }

  /**
   * Load alerts from storage
   */
  private async loadAlerts(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('security_alerts');
      if (stored) {
        this.alerts = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading security alerts:', error);
    }
  }

  /**
   * Save alerts to storage
   */
  private async saveAlerts(): Promise<void> {
    try {
      await AsyncStorage.setItem('security_alerts', JSON.stringify(this.alerts));
    } catch (error) {
      console.error('Error saving security alerts:', error);
    }
  }
}
