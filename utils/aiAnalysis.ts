import { FileInfo, StorageInfo, formatBytes } from './storageUtils';

export interface AIRecommendation {
  id: string;
  type: 'cleanup' | 'compression' | 'organization' | 'warning' | 'optimization';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  action: string;
  potentialSavings?: number;
  affectedFiles?: FileInfo[];
  confidence: number; // 0-100
}

export interface AIInsight {
  category: string;
  insight: string;
  impact: 'positive' | 'negative' | 'neutral';
  recommendation?: string;
}

/**
 * Analyze storage and provide AI-powered recommendations
 */
export const analyzeStorageWithAI = async (
  storageInfo: StorageInfo,
  files: FileInfo[]
): Promise<{
  recommendations: AIRecommendation[];
  insights: AIInsight[];
  overallScore: number;
}> => {
  const recommendations: AIRecommendation[] = [];
  const insights: AIInsight[] = [];

  // Analyze storage usage patterns
  const usagePercentage = (storageInfo.usedSpace / storageInfo.totalSpace) * 100;

  // Critical storage warning
  if (usagePercentage > 90) {
    recommendations.push({
      id: 'critical-storage',
      type: 'warning',
      priority: 'high',
      title: 'Critical Storage Alert',
      description: `Your device is ${usagePercentage.toFixed(1)}% full. Immediate action required.`,
      action: 'Free up space immediately',
      confidence: 95,
    });

    insights.push({
      category: 'Storage Health',
      insight: 'Device storage is critically low',
      impact: 'negative',
      recommendation: 'Delete unnecessary files or move them to cloud storage',
    });
  } else if (usagePercentage > 80) {
    recommendations.push({
      id: 'high-storage',
      type: 'warning',
      priority: 'medium',
      title: 'High Storage Usage',
      description: `Your device is ${usagePercentage.toFixed(1)}% full. Consider cleaning up.`,
      action: 'Review and clean files',
      confidence: 85,
    });
  }

  // Analyze file patterns
  const largeFiles = files.filter(f => f.size > 100 * 1024 * 1024); // > 100MB
  const oldFiles = files.filter(f => {
    const fileAge = Date.now() - (f.modificationTime || 0);
    return fileAge > 365 * 24 * 60 * 60 * 1000; // > 1 year old
  });

  // Large files recommendation
  if (largeFiles.length > 0) {
    const totalLargeSize = largeFiles.reduce((sum, f) => sum + f.size, 0);
    recommendations.push({
      id: 'large-files',
      type: 'cleanup',
      priority: 'medium',
      title: 'Large Files Detected',
      description: `Found ${largeFiles.length} files larger than 100MB`,
      action: 'Review and compress or delete',
      potentialSavings: totalLargeSize * 0.6, // Estimate 60% savings
      affectedFiles: largeFiles.slice(0, 10), // Top 10
      confidence: 80,
    });
  }

  // Old files recommendation
  if (oldFiles.length > 0) {
    const totalOldSize = oldFiles.reduce((sum, f) => sum + f.size, 0);
    recommendations.push({
      id: 'old-files',
      type: 'cleanup',
      priority: 'low',
      title: 'Old Files Found',
      description: `Found ${oldFiles.length} files older than 1 year`,
      action: 'Archive or delete old files',
      potentialSavings: totalOldSize,
      affectedFiles: oldFiles.slice(0, 10),
      confidence: 70,
    });
  }

  // Duplicate files analysis
  const duplicateGroups = await findPotentialDuplicates(files);
  if (Object.keys(duplicateGroups).length > 0) {
    const totalDuplicateSize = Object.values(duplicateGroups)
      .flat()
      .reduce((sum, f) => sum + f.size, 0);
    
    recommendations.push({
      id: 'duplicate-files',
      type: 'cleanup',
      priority: 'medium',
      title: 'Duplicate Files Detected',
      description: `Found potential duplicate files wasting ${formatBytes(totalDuplicateSize)}`,
      action: 'Remove duplicate files',
      potentialSavings: totalDuplicateSize * 0.8, // Keep one copy
      confidence: 75,
    });
  }

  // Media compression opportunities
  const imageFiles = files.filter(f => f.type === 'image');
  const videoFiles = files.filter(f => f.type === 'video');
  
  if (imageFiles.length > 50) {
    const totalImageSize = imageFiles.reduce((sum, f) => sum + f.size, 0);
    recommendations.push({
      id: 'compress-images',
      type: 'compression',
      priority: 'medium',
      title: 'Image Compression Opportunity',
      description: `${imageFiles.length} images could be compressed`,
      action: 'Compress images to save space',
      potentialSavings: totalImageSize * 0.4, // Estimate 40% savings
      affectedFiles: imageFiles.slice(0, 20),
      confidence: 85,
    });
  }

  // Storage breakdown insights
  if (storageInfo.breakdown) {
    const breakdown = storageInfo.breakdown;
    const total = storageInfo.usedSpace;

    // Check for unusual patterns
    if (breakdown.videos / total > 0.5) {
      insights.push({
        category: 'Usage Pattern',
        insight: 'Videos consume over 50% of your storage',
        impact: 'negative',
        recommendation: 'Consider moving videos to cloud storage or compressing them',
      });
    }

    if (breakdown.images / total > 0.3) {
      insights.push({
        category: 'Usage Pattern',
        insight: 'Photos take up significant storage space',
        impact: 'neutral',
        recommendation: 'Enable cloud photo backup and remove local copies',
      });
    }

    if (breakdown.other / total > 0.4) {
      insights.push({
        category: 'Usage Pattern',
        insight: 'Large amount of miscellaneous files detected',
        impact: 'negative',
        recommendation: 'Review and organize miscellaneous files',
      });
    }
  }

  // Calculate overall storage health score
  let score = 100;
  score -= Math.max(0, (usagePercentage - 70) * 2); // Penalty for high usage
  score -= Math.min(20, largeFiles.length * 2); // Penalty for large files
  score -= Math.min(15, Object.keys(duplicateGroups).length * 3); // Penalty for duplicates
  score = Math.max(0, Math.min(100, score));

  return {
    recommendations: recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    }),
    insights,
    overallScore: Math.round(score),
  };
};

/**
 * Find potential duplicate files using smart heuristics
 */
const findPotentialDuplicates = async (files: FileInfo[]): Promise<{ [key: string]: FileInfo[] }> => {
  const duplicates: { [key: string]: FileInfo[] } = {};
  
  // Group by size first (files of different sizes can't be duplicates)
  const sizeGroups: { [size: number]: FileInfo[] } = {};
  
  files.forEach(file => {
    if (file.size > 0) {
      if (!sizeGroups[file.size]) {
        sizeGroups[file.size] = [];
      }
      sizeGroups[file.size].push(file);
    }
  });

  // Check groups with multiple files
  Object.entries(sizeGroups).forEach(([size, groupFiles]) => {
    if (groupFiles.length > 1) {
      // Further group by name similarity
      const nameGroups: { [pattern: string]: FileInfo[] } = {};
      
      groupFiles.forEach(file => {
        // Create a pattern from filename (remove numbers, extensions)
        const pattern = file.name
          .toLowerCase()
          .replace(/\d+/g, '')
          .replace(/\.[^.]+$/, '')
          .replace(/[^a-z]/g, '');
        
        if (pattern.length > 2) {
          if (!nameGroups[pattern]) {
            nameGroups[pattern] = [];
          }
          nameGroups[pattern].push(file);
        }
      });

      // Add groups with potential duplicates
      Object.entries(nameGroups).forEach(([pattern, patternFiles]) => {
        if (patternFiles.length > 1) {
          duplicates[`${pattern}_${size}`] = patternFiles;
        }
      });
    }
  });

  return duplicates;
};

/**
 * Generate personalized storage tips based on usage patterns
 */
export const generatePersonalizedTips = (
  storageInfo: StorageInfo,
  userBehavior: {
    frequentlyUsedApps: string[];
    photosTaken: number;
    videosRecorded: number;
    downloadsCount: number;
  }
): string[] => {
  const tips: string[] = [];

  if (userBehavior.photosTaken > 100) {
    tips.push('Consider enabling automatic cloud backup for photos to free up local storage');
  }

  if (userBehavior.videosRecorded > 20) {
    tips.push('Videos take up significant space. Try recording in lower quality for casual videos');
  }

  if (userBehavior.downloadsCount > 50) {
    tips.push('Regularly clean your Downloads folder to prevent accumulation of unnecessary files');
  }

  const usagePercentage = (storageInfo.usedSpace / storageInfo.totalSpace) * 100;
  if (usagePercentage > 85) {
    tips.push('Your storage is nearly full. Consider upgrading to a higher capacity device or using cloud storage');
  }

  return tips;
};
