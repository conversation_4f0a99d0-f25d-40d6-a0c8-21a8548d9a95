import AsyncStorage from '@react-native-async-storage/async-storage';
import { getStorageInfo, StorageInfo } from './storageUtils';

export interface PerformanceMetrics {
  timestamp: number;
  storageUsage: number;
  freeSpace: number;
  appLaunchTime: number;
  scanDuration: number;
  cleanupEfficiency: number;
  compressionRatio: number;
}

export interface OptimizationSuggestion {
  id: string;
  category: 'performance' | 'storage' | 'battery' | 'memory';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedImprovement: string;
  action: () => Promise<void>;
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private readonly STORAGE_KEY = 'performance_metrics';
  private readonly MAX_METRICS = 100; // Keep last 100 metrics

  private constructor() {
    this.loadMetrics();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Record a performance metric
   */
  async recordMetric(metric: Partial<PerformanceMetrics>): Promise<void> {
    const fullMetric: PerformanceMetrics = {
      timestamp: Date.now(),
      storageUsage: 0,
      freeSpace: 0,
      appLaunchTime: 0,
      scanDuration: 0,
      cleanupEfficiency: 0,
      compressionRatio: 0,
      ...metric,
    };

    this.metrics.push(fullMetric);

    // Keep only the last MAX_METRICS entries
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    await this.saveMetrics();
  }

  /**
   * Get performance trends
   */
  getPerformanceTrends(): {
    storageUsageTrend: number[];
    cleanupEfficiencyTrend: number[];
    compressionRatioTrend: number[];
    timestamps: number[];
  } {
    const last30Days = this.metrics.filter(
      m => m.timestamp > Date.now() - 30 * 24 * 60 * 60 * 1000
    );

    return {
      storageUsageTrend: last30Days.map(m => m.storageUsage),
      cleanupEfficiencyTrend: last30Days.map(m => m.cleanupEfficiency),
      compressionRatioTrend: last30Days.map(m => m.compressionRatio),
      timestamps: last30Days.map(m => m.timestamp),
    };
  }

  /**
   * Analyze performance and generate optimization suggestions
   */
  async generateOptimizationSuggestions(): Promise<OptimizationSuggestion[]> {
    const suggestions: OptimizationSuggestion[] = [];
    const recentMetrics = this.metrics.slice(-10); // Last 10 metrics
    const storageInfo = await getStorageInfo();

    // Storage optimization suggestions
    const avgStorageUsage = recentMetrics.reduce((sum, m) => sum + m.storageUsage, 0) / recentMetrics.length;
    if (avgStorageUsage > 80) {
      suggestions.push({
        id: 'high-storage-usage',
        category: 'storage',
        title: 'High Storage Usage Detected',
        description: 'Your device storage is consistently above 80% full',
        impact: 'high',
        difficulty: 'easy',
        estimatedImprovement: 'Free up 10-30% storage space',
        action: async () => {
          // Trigger automatic cleanup
          console.log('Triggering automatic cleanup...');
        },
      });
    }

    // Performance optimization suggestions
    const avgScanDuration = recentMetrics.reduce((sum, m) => sum + m.scanDuration, 0) / recentMetrics.length;
    if (avgScanDuration > 5000) { // More than 5 seconds
      suggestions.push({
        id: 'slow-scan-performance',
        category: 'performance',
        title: 'Slow File Scanning',
        description: 'File scanning is taking longer than usual',
        impact: 'medium',
        difficulty: 'medium',
        estimatedImprovement: 'Reduce scan time by 50%',
        action: async () => {
          // Optimize scan algorithm
          console.log('Optimizing scan algorithm...');
        },
      });
    }

    // Compression efficiency suggestions
    const avgCompressionRatio = recentMetrics.reduce((sum, m) => sum + m.compressionRatio, 0) / recentMetrics.length;
    if (avgCompressionRatio < 0.3) { // Less than 30% compression
      suggestions.push({
        id: 'poor-compression',
        category: 'storage',
        title: 'Improve Compression Settings',
        description: 'Current compression settings are not optimal',
        impact: 'medium',
        difficulty: 'easy',
        estimatedImprovement: 'Increase compression efficiency by 20%',
        action: async () => {
          // Adjust compression settings
          console.log('Adjusting compression settings...');
        },
      });
    }

    // Memory optimization suggestions
    if (storageInfo.breakdown) {
      const { breakdown } = storageInfo;
      const totalUsed = storageInfo.usedSpace;

      if (breakdown.other / totalUsed > 0.4) {
        suggestions.push({
          id: 'organize-miscellaneous',
          category: 'storage',
          title: 'Organize Miscellaneous Files',
          description: 'Large amount of unorganized files detected',
          impact: 'medium',
          difficulty: 'easy',
          estimatedImprovement: 'Better file organization and faster access',
          action: async () => {
            // Trigger file organization
            console.log('Organizing miscellaneous files...');
          },
        });
      }
    }

    // Battery optimization suggestions
    suggestions.push({
      id: 'optimize-background-tasks',
      category: 'battery',
      title: 'Optimize Background Tasks',
      description: 'Reduce background processing to save battery',
      impact: 'low',
      difficulty: 'easy',
      estimatedImprovement: 'Extend battery life by 10-15%',
      action: async () => {
        // Optimize background tasks
        console.log('Optimizing background tasks...');
      },
    });

    return suggestions.sort((a, b) => {
      const impactOrder = { high: 3, medium: 2, low: 1 };
      return impactOrder[b.impact] - impactOrder[a.impact];
    });
  }

  /**
   * Get performance score (0-100)
   */
  getPerformanceScore(): number {
    if (this.metrics.length === 0) return 50; // Default score

    const recentMetrics = this.metrics.slice(-5); // Last 5 metrics
    let score = 100;

    // Storage usage penalty
    const avgStorageUsage = recentMetrics.reduce((sum, m) => sum + m.storageUsage, 0) / recentMetrics.length;
    score -= Math.max(0, (avgStorageUsage - 70) * 2);

    // Scan performance penalty
    const avgScanDuration = recentMetrics.reduce((sum, m) => sum + m.scanDuration, 0) / recentMetrics.length;
    score -= Math.max(0, (avgScanDuration - 3000) / 100); // Penalty for scans > 3 seconds

    // Cleanup efficiency bonus
    const avgCleanupEfficiency = recentMetrics.reduce((sum, m) => sum + m.cleanupEfficiency, 0) / recentMetrics.length;
    score += avgCleanupEfficiency * 10;

    // Compression ratio bonus
    const avgCompressionRatio = recentMetrics.reduce((sum, m) => sum + m.compressionRatio, 0) / recentMetrics.length;
    score += avgCompressionRatio * 20;

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Start performance monitoring session
   */
  startMonitoringSession(): { sessionId: string; startTime: number } {
    const sessionId = `session_${Date.now()}`;
    const startTime = Date.now();
    
    console.log(`Performance monitoring session started: ${sessionId}`);
    
    return { sessionId, startTime };
  }

  /**
   * End performance monitoring session and record metrics
   */
  async endMonitoringSession(
    sessionId: string,
    startTime: number,
    additionalMetrics: Partial<PerformanceMetrics> = {}
  ): Promise<void> {
    const duration = Date.now() - startTime;
    const storageInfo = await getStorageInfo();
    
    await this.recordMetric({
      scanDuration: duration,
      storageUsage: (storageInfo.usedSpace / storageInfo.totalSpace) * 100,
      freeSpace: storageInfo.freeSpace,
      ...additionalMetrics,
    });

    console.log(`Performance monitoring session ended: ${sessionId}, Duration: ${duration}ms`);
  }

  /**
   * Load metrics from storage
   */
  private async loadMetrics(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.metrics = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading performance metrics:', error);
    }
  }

  /**
   * Save metrics to storage
   */
  private async saveMetrics(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.metrics));
    } catch (error) {
      console.error('Error saving performance metrics:', error);
    }
  }

  /**
   * Clear all metrics
   */
  async clearMetrics(): Promise<void> {
    this.metrics = [];
    await AsyncStorage.removeItem(this.STORAGE_KEY);
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): string {
    return JSON.stringify({
      metrics: this.metrics,
      exportDate: new Date().toISOString(),
      version: '1.0',
    }, null, 2);
  }

  /**
   * Get system health status
   */
  async getSystemHealth(): Promise<{
    status: 'excellent' | 'good' | 'fair' | 'poor';
    score: number;
    issues: string[];
    recommendations: string[];
  }> {
    const score = this.getPerformanceScore();
    const storageInfo = await getStorageInfo();
    const usagePercentage = (storageInfo.usedSpace / storageInfo.totalSpace) * 100;
    
    const issues: string[] = [];
    const recommendations: string[] = [];

    if (usagePercentage > 90) {
      issues.push('Critical storage usage');
      recommendations.push('Free up storage space immediately');
    } else if (usagePercentage > 80) {
      issues.push('High storage usage');
      recommendations.push('Consider cleaning up files');
    }

    if (this.metrics.length > 0) {
      const recentMetrics = this.metrics.slice(-5);
      const avgScanDuration = recentMetrics.reduce((sum, m) => sum + m.scanDuration, 0) / recentMetrics.length;
      
      if (avgScanDuration > 5000) {
        issues.push('Slow file scanning');
        recommendations.push('Optimize file organization');
      }
    }

    let status: 'excellent' | 'good' | 'fair' | 'poor';
    if (score >= 90) status = 'excellent';
    else if (score >= 75) status = 'good';
    else if (score >= 60) status = 'fair';
    else status = 'poor';

    return { status, score, issues, recommendations };
  }
}
