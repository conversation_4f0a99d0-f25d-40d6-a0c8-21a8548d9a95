import * as Crypto from 'expo-crypto';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import * as MediaLibrary from 'expo-media-library';

export interface FileInfo {
  id?: string;
  name: string;
  path: string;
  uri: string;
  size: number;
  modificationTime?: number;
  isDirectory?: boolean;
  type?: string;
  selected?: boolean;
}

export interface StorageInfo {
  totalSpace: number;
  freeSpace: number;
  usedSpace: number;
  breakdown?: {
    images: number;
    videos: number;
    audio: number;
    documents: number;
    apps: number;
    system: number;
    other: number;
  };
}

export interface StorageTrend {
  date: string;
  usedSpace: number;
  freeSpace: number;
}

export interface FileTypeBreakdown {
  type: string;
  size: number;
  count: number;
  percentage: number;
}

/**
 * Get device storage information with detailed breakdown
 */
export const getStorageInfo = async (): Promise<StorageInfo> => {
  try {
    const [freeSpace, totalSpace] = await Promise.all([
      FileSystem.getFreeDiskStorageAsync(),
      FileSystem.getTotalDiskCapacityAsync()
    ]);

    const usedSpace = totalSpace - freeSpace;

    // Get detailed breakdown
    const breakdown = await getStorageBreakdown();

    return {
      totalSpace,
      freeSpace,
      usedSpace,
      breakdown,
    };
  } catch (error) {
    console.error('Error getting storage info:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to retrieve storage information: ${errorMessage}`);
  }
};

/**
 * Get storage breakdown by file types
 */
export const getStorageBreakdown = async (): Promise<{
  images: number;
  videos: number;
  audio: number;
  documents: number;
  apps: number;
  system: number;
  other: number;
}> => {
  try {
    const breakdown = {
      images: 0,
      videos: 0,
      audio: 0,
      documents: 0,
      apps: 0,
      system: 0,
      other: 0,
    };

    // Get media files and categorize them
    const mediaFiles = await getMediaFiles();

    for (const file of mediaFiles) {
      if (file.type === 'image') {
        breakdown.images += file.size;
      } else if (file.type === 'video') {
        breakdown.videos += file.size;
      } else if (file.type === 'audio') {
        breakdown.audio += file.size;
      }
    }

    // Estimate other categories (this is a simplified approach)
    // In a real app, you'd need more sophisticated file system access
    const totalMediaSize = breakdown.images + breakdown.videos + breakdown.audio;
    const storageInfo = await FileSystem.getFreeDiskStorageAsync();
    const totalUsed = (await FileSystem.getTotalDiskCapacityAsync()) - storageInfo;

    // Rough estimates based on typical device usage patterns
    breakdown.apps = totalUsed * 0.3; // ~30% apps
    breakdown.system = totalUsed * 0.25; // ~25% system
    breakdown.documents = totalUsed * 0.05; // ~5% documents
    breakdown.other = totalUsed - totalMediaSize - breakdown.apps - breakdown.system - breakdown.documents;

    return breakdown;
  } catch (error) {
    console.error('Error getting storage breakdown:', error);
    return {
      images: 0,
      videos: 0,
      audio: 0,
      documents: 0,
      apps: 0,
      system: 0,
      other: 0,
    };
  }
};

/**
 * Get storage usage trends (mock data for now)
 */
export const getStorageTrends = async (): Promise<StorageTrend[]> => {
  try {
    // In a real implementation, this would read from stored historical data
    const currentStorage = await getStorageInfo();
    const trends: StorageTrend[] = [];

    // Generate mock trend data for the last 30 days
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      // Simulate gradual storage usage increase
      const variation = Math.random() * 0.1 - 0.05; // ±5% variation
      const usedSpace = currentStorage.usedSpace * (0.8 + (29 - i) * 0.007 + variation);
      const freeSpace = currentStorage.totalSpace - usedSpace;

      trends.push({
        date: date.toISOString().split('T')[0],
        usedSpace,
        freeSpace,
      });
    }

    return trends;
  } catch (error) {
    console.error('Error getting storage trends:', error);
    return [];
  }
};

/**
 * Format bytes to human readable format
 */
export const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Get file type from file path
 */
export const getFileType = (filePath: string): string => {
  const extension = filePath.split('.').pop()?.toLowerCase() || '';

  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
  const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'];
  const audioExts = ['mp3', 'wav', 'ogg', 'aac', 'm4a', 'flac'];
  const docExts = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'];
  const spreadsheetExts = ['xls', 'xlsx', 'csv', 'ods'];
  const presentationExts = ['ppt', 'pptx', 'odp'];
  const archiveExts = ['zip', 'rar', '7z', 'tar', 'gz'];

  if (imageExts.includes(extension)) return 'image';
  if (videoExts.includes(extension)) return 'video';
  if (audioExts.includes(extension)) return 'audio';
  if (docExts.includes(extension)) return 'document';
  if (spreadsheetExts.includes(extension)) return 'spreadsheet';
  if (presentationExts.includes(extension)) return 'presentation';
  if (archiveExts.includes(extension)) return 'archive';

  return 'other';
};

/**
 * Get list of media files from the device (photos and videos)
 */
export const getMediaFiles = async (): Promise<FileInfo[]> => {
  try {
    // Request proper permissions including location access
    const permissionsGranted = await requestStoragePermissions();

    if (!permissionsGranted) {
      throw new Error('Media library permission not granted');
    }

    const media = await MediaLibrary.getAssetsAsync({
      mediaType: ['photo', 'video'],
      first: 1000,
    });

    if (!media || !media.assets || media.assets.length === 0) {
      console.log('No media assets found');
      return [];
    }

    // Skip detailed asset info to avoid ExifInterface access issues
    // This is a workaround for the ACCESS_MEDIA_LOCATION permission issue
    const mediaInfo: FileInfo[] = media.assets.map(item => {
      return {
        id: item.id,
        name: `${item.id}.${item.mediaType === 'photo' ? 'jpg' : 'mp4'}`,
        path: item.uri,
        uri: item.uri,
        size: 0, // We don't have file size without getAssetInfoAsync
        modificationTime: item.modificationTime,
        type: item.mediaType === 'photo' ? 'image' : 'video',
        selected: false,
      };
    });

    return mediaInfo;
  } catch (error) {
    console.error('Error getting media files:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to retrieve media files: ${errorMessage}`);
  }
};

/**
 * Get list of large files from the device (> 50MB)
 * Implements enhanced file size estimation and file discovery
 */
export const getLargeFiles = async (): Promise<FileInfo[]> => {
  try {
    // Request proper permissions including location access
    const permissionsGranted = await requestStoragePermissions();

    if (!permissionsGranted) {
      throw new Error('Media library permission not granted');
    }

    const results: FileInfo[] = [];

    // Get media assets with better information
    const media = await MediaLibrary.getAssetsAsync({
      mediaType: ['photo', 'video'],
      first: 1000,
      sortBy: [MediaLibrary.SortBy.creationTime],
    });

    // Process media assets
    for (const asset of media.assets) {
      try {
        // For video files, use the duration to estimate size more accurately
        // For images, use width/height to estimate size
        let estimatedSize = 0;

        if (asset.mediaType === 'video') {
          // Estimate video size: ~2MB per second for HD video
          estimatedSize = Math.round(asset.duration * 2 * 1024 * 1024);
        } else {
          // Estimate image size: ~0.25 bytes per pixel (accounting for compression)
          estimatedSize = Math.round((asset.width * asset.height) / 4);
        }

        // Only include if it's likely to be large (> 50MB)
        if (estimatedSize > 50 * 1024 * 1024 ||
            (asset.mediaType === 'video' && asset.duration > 180) || // Videos > 3 min
            (asset.mediaType === 'photo' && (asset.width * asset.height) > ********)) { // > 12MP images

          results.push({
            id: asset.id,
            name: `${asset.filename || asset.id}${asset.mediaType === 'photo' ? '.jpg' : '.mp4'}`,
            path: asset.uri,
            uri: asset.uri,
            size: estimatedSize,
            modificationTime: asset.modificationTime,
            type: asset.mediaType === 'photo' ? 'image' : 'video',
            selected: false,
          });
        }
      } catch (assetError) {
        console.warn(`Error processing asset ${asset.id}:`, assetError);
      }
    }

    // Try to get additional large files from accessible directories
    try {
      const directories = [
        FileSystem.documentDirectory,
        FileSystem.cacheDirectory,
      ];

      for (const directory of directories) {
        if (!directory) continue;

        const contents = await getDirectoryContents(directory);

        // Get file info and filter for large files
        for (const file of contents) {
          try {
            if (!file.isDirectory && file.size > 50 * 1024 * 1024) {
              results.push(file);
            } else if (file.isDirectory) {
              // Look one level deep in directories
              try {
                const subContents = await getDirectoryContents(file.uri);
                for (const subFile of subContents) {
                  if (!subFile.isDirectory && subFile.size > 50 * 1024 * 1024) {
                    results.push(subFile);
                  }
                }
              } catch (subDirError) {
                console.warn(`Error reading subdirectory ${file.name}:`, subDirError);
              }
            }
          } catch (fileError) {
            console.warn(`Error processing file ${file.name}:`, fileError);
          }
        }
      }
    } catch (dirError) {
      console.warn('Error scanning directories for large files:', dirError);
    }

    // Sort by size (largest first) and return
    return results.sort((a, b) => b.size - a.size);
  } catch (error) {
    console.error('Error getting large files:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to retrieve large files: ${errorMessage}`);
  }
};

/**
 * Get file directory contents
 */
export const getDirectoryContents = async (directoryUri: string): Promise<FileInfo[]> => {
  try {
    const contents = await FileSystem.readDirectoryAsync(directoryUri);

    const fileInfos = await Promise.all(
      contents.map(async (name) => {
        const path = `${directoryUri}${directoryUri.endsWith('/') ? '' : '/'}${name}`;
        const fileInfo = await FileSystem.getInfoAsync(path);

        return {
          name,
          path,
          uri: path,
          size: (fileInfo as any).size || 0,
          modificationTime: (fileInfo as any).modificationTime || 0,
          isDirectory: fileInfo.isDirectory || false,
          type: fileInfo.isDirectory ? 'directory' : getFileType(name),
          selected: false,
        };
      })
    );

    return fileInfos;
  } catch (error) {
    console.error('Error reading directory:', error);
    return [];
  }
};

/**
 * Delete file
 */
export const deleteFile = async (fileUri: string): Promise<boolean> => {
  try {
    await FileSystem.deleteAsync(fileUri);
    return true;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};

/**
 * Compress image file and save to the cache directory
 * @param imageUri URI of the image to compress
 * @param quality Compression quality (0 to 1, where 1 is highest quality)
 * @returns URI of the compressed image or null if compression failed
 */
export const compressImage = async (
  imageUri: string,
  quality: number = 0.5
): Promise<string | null> => {
  try {
    // Ensure the quality is within the valid range
    const validQuality = Math.max(0.1, Math.min(1.0, quality));

    // Check if the input file exists
    const fileInfo = await FileSystem.getInfoAsync(imageUri);
    if (!fileInfo.exists) {
      console.error(`Image file does not exist: ${imageUri}`);
      return null;
    }

    // Use ImageManipulator for the actual compression
    const result = await ImageManipulator.manipulateAsync(
      imageUri,
      [], // No image transformations, just compression
      {
        compress: validQuality,
        format: ImageManipulator.SaveFormat.JPEG,
        base64: false
      }
    );

    if (!result || !result.uri) {
      console.error('Image manipulation returned no result URI');
      return null;
    }

    // Ensure the output file exists
    const outputFileInfo = await FileSystem.getInfoAsync(result.uri);
    if (!outputFileInfo.exists) {
      console.error(`Compressed image file was not created: ${result.uri}`);
      return null;
    }

    // Save the file to a more permanent location if it's in a temporary directory
    if (result.uri.includes('ImageManipulator') || result.uri.includes('/tmp/')) {
      const fileName = `compressed_${Date.now()}.jpg`;
      const destinationUri = `${FileSystem.cacheDirectory}${fileName}`;

      try {
        await FileSystem.copyAsync({
          from: result.uri,
          to: destinationUri
        });

        // Verify the file was copied successfully
        const destFileInfo = await FileSystem.getInfoAsync(destinationUri);
        if (destFileInfo.exists) {
          console.log(`Compressed image saved to: ${destinationUri}`);
          return destinationUri;
        }
      } catch (copyError) {
        console.error('Error copying compressed file:', copyError);
        // If copy fails, return the original result URI
        return result.uri;
      }
    }

    // Return the original result URI if we didn't need to copy it
    return result.uri;
  } catch (error) {
    console.error('Error compressing image:', error);
    return null;
  }
};

/**
 * Pick document
 */
export const pickDocument = async (): Promise<FileInfo | null> => {
  try {
    const result = await DocumentPicker.getDocumentAsync({
      copyToCacheDirectory: false,
      type: '*/*',
    });

    if (result.canceled) {
      return null;
    }

    const asset = result.assets[0];
    const fileInfo = await FileSystem.getInfoAsync(asset.uri);

    return {
      name: asset.name || 'Unknown',
      path: asset.uri,
      uri: asset.uri,
      size: (fileInfo as any).size || asset.size || 0,
      modificationTime: (fileInfo as any).modificationTime || 0,
      type: getFileType(asset.name || ''),
      selected: false,
    };
  } catch (error) {
    console.error('Error picking document:', error);
    return null;
  }
};

/**
 * Find duplicate files by comparing file metadata and content hashing
 * Uses a multi-stage approach for better efficiency
 */
export const findDuplicateFiles = async (files: FileInfo[]): Promise<{ [hash: string]: FileInfo[] }> => {
  const fileHashes: { [hash: string]: FileInfo[] } = {};

  // First group by size (files of different sizes can't be duplicates)
  const filesBySize: { [size: number]: FileInfo[] } = {};

  // First pass: group by size
  for (const file of files) {
    // Skip files with zero size
    if (file.size <= 0) continue;

    if (!filesBySize[file.size]) {
      filesBySize[file.size] = [];
    }
    filesBySize[file.size].push(file);
  }

  // Second pass: compare by file name pattern and media type first
  // This can catch many duplicates without having to read file content
  const potentialDuplicateGroups: FileInfo[][] = [];

  for (const size in filesBySize) {
    const sizeGroup = filesBySize[size];

    // Skip singleton size groups (no duplicates possible)
    if (sizeGroup.length < 2) continue;

    // Group by filename pattern
    const filenameGroups: { [pattern: string]: FileInfo[] } = {};

    for (const file of sizeGroup) {
      // Extract the base name without extension and numbers
      const baseName = file.name.replace(/[0-9]+|\..*$/g, '').toLowerCase().trim();

      // Skip if base name is empty or too short (likely not meaningful)
      if (baseName.length < 3) continue;

      if (!filenameGroups[baseName]) {
        filenameGroups[baseName] = [];
      }
      filenameGroups[baseName].push(file);
    }

    // Add potential duplicate groups from filename patterns
    for (const pattern in filenameGroups) {
      if (filenameGroups[pattern].length >= 2) {
        potentialDuplicateGroups.push(filenameGroups[pattern]);
      }
    }

    // For files that didn't match by name pattern, proceed with content hash
    if (sizeGroup.length >= 2) {
      const contentDuplicates = await findDuplicatesByContent(sizeGroup);
      Object.values(contentDuplicates).forEach(group => {
        if (group.length >= 2) {
          potentialDuplicateGroups.push(group);
        }
      });
    }
  }

  // Third pass: organize results by hash for consistency with return type
  let duplicateCounter = 0;

  for (const group of potentialDuplicateGroups) {
    if (group.length >= 2) {
      const hashKey = `duplicate_group_${duplicateCounter++}`;
      fileHashes[hashKey] = group;
    }
  }

  return fileHashes;
};

/**
 * Helper function to find duplicates by content hashing
 */
async function findDuplicatesByContent(files: FileInfo[]): Promise<{ [hash: string]: FileInfo[] }> {
  const contentHashes: { [hash: string]: FileInfo[] } = {};

  for (const file of files) {
    try {
      // Check if file exists and is readable before attempting to read
      const fileInfo = await FileSystem.getInfoAsync(file.uri);
      if (!fileInfo.exists) {
        console.warn(`File does not exist: ${file.name}`);
        continue;
      }

      // Only read the beginning of the file (first 16KB)
      const fileHeader = await FileSystem.readAsStringAsync(
        file.uri,
        {
          encoding: FileSystem.EncodingType.Base64,
          length: 16 * 1024, // 16KB
          position: 0
        }
      );

      // Hash the partial content
      const partialHash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        fileHeader + file.size // Include size in hash to reduce false positives
      );

      // Group by partial hash
      if (!contentHashes[partialHash]) {
        contentHashes[partialHash] = [];
      }

      contentHashes[partialHash].push(file);
    } catch (error) {
      if (error instanceof Error) {
        console.error(`Error hashing file ${file.name}: ${error.message}`);
      } else {
        console.error(`Unknown error hashing file ${file.name}`);
      }
    }
  }

  // Filter out entries with only one file (not duplicates)
  Object.keys(contentHashes).forEach(hash => {
    if (contentHashes[hash].length < 2) {
      delete contentHashes[hash];
    }
  });

  return contentHashes;
}

/**
 * Find old files (not accessed in the last 3 months)
 */
export const findOldFiles = (files: FileInfo[], monthsOld: number = 3): FileInfo[] => {
  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - monthsOld);

  return files.filter(file => {
    if (!file.modificationTime) return false;
    const modDate = new Date(file.modificationTime);
    return modDate < threeMonthsAgo;
  });
};

/**
 * Get app cache files (only works with proper permissions on Android)
 */
export const getAppCacheFiles = async (): Promise<FileInfo[]> => {
  try {
    // This is a list of common cache directories to check
    const cacheDirs = [
      FileSystem.cacheDirectory,
      `${FileSystem.documentDirectory}cache/`,
      `${FileSystem.documentDirectory}temp/`,
    ];

    let cacheFiles: FileInfo[] = [];

    for (const dir of cacheDirs) {
      if (!dir) continue;

      try {
        const exists = await FileSystem.getInfoAsync(dir);
        if (exists.exists && exists.isDirectory) {
          const dirContents = await getDirectoryContents(dir);
          cacheFiles = [...cacheFiles, ...dirContents.filter(file => !file.isDirectory)];

          // Recursively check subdirectories (1 level deep)
          for (const item of dirContents) {
            if (item.isDirectory) {
              const subdirContents = await getDirectoryContents(item.uri);
              cacheFiles = [...cacheFiles, ...subdirContents.filter(file => !file.isDirectory)];
            }
          }
        }
      } catch (error) {
        console.warn(`Error reading cache directory ${dir}:`, error);
      }
    }

    return cacheFiles.map(file => ({
      ...file,
      type: 'cache'
    }));
  } catch (error) {
    console.error('Error getting app cache files:', error);
    return [];
  }
};

/**
 * Get temporary files and logs that can be safely deleted
 */
export const getJunkFiles = async (): Promise<FileInfo[]> => {
  try {
    const documentDir = FileSystem.documentDirectory;
    if (!documentDir) {
      throw new Error('Document directory not available');
    }

    let junkFiles: FileInfo[] = [];

    // Look for common junk file patterns in document directory
    const filePatterns = ['.log', '.tmp', '.temp', '.cache', '.bak'];

    const contents = await getDirectoryContents(documentDir);

    // Find files matching junk patterns
    junkFiles = contents.filter(
      file => !file.isDirectory &&
      filePatterns.some(pattern => file.name.toLowerCase().endsWith(pattern))
    );

    // Also look for any files in temp directories
    try {
      const tempDir = `${documentDir}temp/`;
      const tempDirExists = await FileSystem.getInfoAsync(tempDir);

      if (tempDirExists.exists && tempDirExists.isDirectory) {
        const tempFiles = await getDirectoryContents(tempDir);
        junkFiles = [...junkFiles, ...tempFiles.filter(file => !file.isDirectory)];
      }
    } catch (error) {
      console.warn('Error accessing temp directory:', error);
    }

    return junkFiles.map(file => ({
      ...file,
      type: 'junk'
    }));
  } catch (error) {
    console.error('Error getting junk files:', error);
    return [];
  }
};

/**
 * Get files that can be safely cleaned based on category
 */
export const getCleanableFiles = async (category: 'junk' | 'cache' | 'appData' | 'downloads'): Promise<FileInfo[]> => {
  try {
    switch (category) {
      case 'junk':
        return await getJunkFiles();
      case 'cache':
        return await getAppCacheFiles();
      case 'appData':
        // For appData, we need to combine different types of application data
        const appDataFiles: FileInfo[] = [];

        // Get downloads that are app-related (like temporary downloads)
        try {
          const downloadDir = `${FileSystem.documentDirectory}downloads/`;
          const downloadDirExists = await FileSystem.getInfoAsync(downloadDir);

          if (downloadDirExists.exists && downloadDirExists.isDirectory) {
            const downloads = await getDirectoryContents(downloadDir);
            const appDataDownloads = downloads.filter(
              file => !file.isDirectory && getFileType(file.name) !== 'image' && getFileType(file.name) !== 'video'
            );
            appDataFiles.push(...appDataDownloads);
          }
        } catch (error) {
          console.warn('Error accessing downloads directory:', error);
        }

        // Check app-specific temp directories
        try {
          const tempDir = `${FileSystem.documentDirectory}temp/`;
          const tempDirExists = await FileSystem.getInfoAsync(tempDir);

          if (tempDirExists.exists && tempDirExists.isDirectory) {
            const tempFiles = await getDirectoryContents(tempDir);
            appDataFiles.push(...tempFiles.filter(file => !file.isDirectory));
          }
        } catch (error) {
          console.warn('Error accessing temp directory:', error);
        }

        return appDataFiles;
      case 'downloads':
        // Look for download files in multiple potential locations
        const downloadFiles: FileInfo[] = [];

        // Check document directory first
        try {
          const documentDir = FileSystem.documentDirectory;
          if (documentDir) {
            const contents = await getDirectoryContents(documentDir);

            // Filter for typical downloaded file types
            const downloadTypes = ['document', 'spreadsheet', 'presentation', 'archive', 'other'];
            const docDownloads = contents.filter(
              file => !file.isDirectory && downloadTypes.includes(getFileType(file.name))
            );

            downloadFiles.push(...docDownloads);
          }
        } catch (error) {
          console.warn('Error accessing document directory:', error);
        }

        // Check specific downloads directory if it exists
        try {
          const downloadDir = `${FileSystem.documentDirectory}downloads/`;
          const downloadDirExists = await FileSystem.getInfoAsync(downloadDir);

          if (downloadDirExists.exists && downloadDirExists.isDirectory) {
            const downloads = await getDirectoryContents(downloadDir);
            downloadFiles.push(...downloads.filter(file => !file.isDirectory));
          }
        } catch (error) {
          console.warn('Error accessing downloads directory:', error);
        }

        // Check cache directory for downloaded files
        try {
          if (FileSystem.cacheDirectory) {
            const cacheContents = await getDirectoryContents(FileSystem.cacheDirectory);

            // Look for files that appear to be downloads in the cache
            const cacheDownloads = cacheContents.filter(
              file => !file.isDirectory &&
                (file.name.includes('download') ||
                 file.name.includes('file') ||
                 file.name.endsWith('.apk') ||
                 file.name.endsWith('.pdf') ||
                 file.name.endsWith('.zip'))
            );

            downloadFiles.push(...cacheDownloads);
          }
        } catch (error) {
          console.warn('Error accessing cache directory for downloads:', error);
        }

        // Check additional directories on device
        try {
          // Try accessing the shared documents directory if available
          const sharedDir = FileSystem.documentDirectory?.replace('Documents/', '');
          if (sharedDir) {
            try {
              const sharedContents = await getDirectoryContents(sharedDir);

              // Look for downloadable file types
              const sharedDownloads = sharedContents.filter(
                file => !file.isDirectory &&
                ['document', 'spreadsheet', 'presentation', 'archive'].includes(getFileType(file.name))
              );

              downloadFiles.push(...sharedDownloads);
            } catch (error) {
              // Ignore errors for shared directory which might not be accessible
              console.warn('Could not access shared directory:', error);
            }
          }
        } catch (error) {
          console.warn('Error accessing additional directories:', error);
        }

        return downloadFiles;
      default:
        return [];
    }
  } catch (error) {
    console.error(`Error getting ${category} files:`, error);
    return [];
  }
};

/**
 * Clean selected files (delete them)
 */
export const cleanFiles = async (files: FileInfo[]): Promise<{ success: number; failed: number }> => {
  let success = 0;
  let failed = 0;

  for (const file of files) {
    try {
      // Check if file exists before attempting to delete
      const fileInfo = await FileSystem.getInfoAsync(file.uri);
      if (!fileInfo.exists) {
        console.warn(`File does not exist: ${file.name}`);
        failed++;
        continue;
      }

      const deleted = await deleteFile(file.uri);
      if (deleted) {
        success++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`Error deleting file ${file.name}:`, error);
      failed++;
    }
  }

  return { success, failed };
};

/**
 * Request all necessary permissions for storage operations
 */
export const requestStoragePermissions = async (): Promise<boolean> => {
  try {
    // First request media library permissions with read access
    const { status } = await MediaLibrary.requestPermissionsAsync(false);

    if (status !== 'granted') {
      console.warn('Media library permission not granted');
      return false;
    }

    // Explicitly request permissions for specific media types on Android 13+
    try {
      // These are the granular permissions for Android 13+
      const granularPermissions = ['photo', 'video'] as MediaLibrary.GranularPermission[];

      // This will trigger the specific ACCESS_MEDIA_LOCATION permission request on appropriate Android versions
      await MediaLibrary.requestPermissionsAsync(false, granularPermissions);

      // On Android 14+, may also need to present permissions picker
      // but this should be handled by the OS automatically
    } catch (granularError) {
      // Just log this as we've already got the main permission
      console.warn('Could not request granular permissions:', granularError);
    }

    return true;
  } catch (error) {
    console.error('Error requesting permissions:', error);
    return false;
  }
};

/**
 * Save image to media library
 * @param uri URI of the image to save
 * @param album Optional album name to save to
 * @returns The ID of the saved asset or null if save failed
 */
export const saveImageToMediaLibrary = async (
  uri: string,
  album?: string
): Promise<string | null> => {
  try {
    // Check if we have permissions
    const { status } = await MediaLibrary.requestPermissionsAsync();
    if (status !== 'granted') {
      console.error('Media library permission not granted');
      return null;
    }

    // Save the image
    const asset = await MediaLibrary.createAssetAsync(uri);

    // If an album is specified, create it if it doesn't exist and add the asset to it
    if (album && asset) {
      try {
        const albums = await MediaLibrary.getAlbumAsync(album);
        if (albums) {
          await MediaLibrary.addAssetsToAlbumAsync([asset], albums, false);
        } else {
          await MediaLibrary.createAlbumAsync(album, asset, false);
        }
      } catch (albumError) {
        console.warn(`Failed to add image to album ${album}:`, albumError);
        // Still return the asset ID even if adding to album fails
      }
    }

    return asset ? asset.id : null;
  } catch (error) {
    console.error('Error saving image to media library:', error);
    return null;
  }
};