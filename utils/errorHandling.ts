import { Alert } from 'react-native';

/**
 * Handle errors in a consistent way across the app
 * @param error The error to handle
 * @param fallbackMessage A fallback message if the error doesn't have a message
 * @param showAlert Whether to show an alert
 * @returns The error message
 */
export const handleError = (
  error: unknown, 
  fallbackMessage = 'An unexpected error occurred', 
  showAlert = true
): string => {
  // Extract the error message
  const errorMessage = error instanceof Error 
    ? error.message 
    : (typeof error === 'string' ? error : fallbackMessage);
  
  // Log the error
  console.error(errorMessage, error);
  
  // Show an alert if requested
  if (showAlert) {
    Alert.alert('Error', errorMessage);
  }
  
  return errorMessage;
};

/**
 * Execute a function with error handling
 * @param fn The function to execute
 * @param fallbackMessage A fallback message if the error doesn't have a message
 * @param showAlert Whether to show an alert
 * @returns The result of the function or null if an error occurred
 */
export async function executeWithErrorHandling<T>(
  fn: () => Promise<T>,
  fallbackMessage = 'An unexpected error occurred',
  showAlert = true
): Promise<T | null> {
  try {
    return await fn();
  } catch (error) {
    handleError(error, fallbackMessage, showAlert);
    return null;
  }
}

/**
 * Execute a function with error handling and return a default value if an error occurs
 * @param fn The function to execute
 * @param defaultValue The default value to return if an error occurs
 * @param fallbackMessage A fallback message if the error doesn't have a message
 * @param showAlert Whether to show an alert
 * @returns The result of the function or the default value if an error occurred
 */
export async function executeWithDefault<T>(
  fn: () => Promise<T>,
  defaultValue: T,
  fallbackMessage = 'An unexpected error occurred',
  showAlert = true
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    handleError(error, fallbackMessage, showAlert);
    return defaultValue;
  }
} 