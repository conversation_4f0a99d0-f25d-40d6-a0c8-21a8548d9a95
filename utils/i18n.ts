import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// English translations
const enTranslations = {
  common: {
    welcome: 'Welcome to StoragePro',
    continue: 'Continue',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    settings: 'Settings',
    back: 'Back',
    grantPermissions: 'Grant Permissions',
    loading: 'Loading...',
    error: 'Error',
    retry: 'Retry',
    free: 'Free',
    used: 'Used',
  },
  tabs: {
    storage: 'Storage',
    analysis: 'Analysis',
    clean: 'Clean',
    compress: 'Compress',
    settings: 'Settings',
  },
  languageSelection: {
    chooseLanguage: 'Choose your language',
    english: 'English',
    hindi: 'Hindi',
  },
  home: {
    title: 'Storage',
    storageUsage: 'Storage Usage',
    usedStorage: 'Used Space',
    freeStorage: 'Available Space',
    quickActions: 'Quick Actions',
    analyzeStorage: 'Analyze Storage',
    cleanStorage: 'Clean Storage',
    compressFiles: 'Compress Files',
    smartAnalysis: 'Smart Analysis',
    aiAssistant: 'Smart Assistant',
    storageTips: 'Storage Tips',
    freeUpSpaceQuickly: 'Free up space quickly',
    freeUpSpaceTip: 'Check the Analyze tab to find large files and duplicates that you can safely remove.',
    compressPhotos: 'Compress your photos',
    compressPhotosTip: 'Use the Compress tab to reduce the size of your photos without losing much quality.',
    mediaBreakdown: 'Media Breakdown',
    photos: 'Photos',
    videos: 'Videos',
    other: 'Other',
    storagePermissionsRequired: 'Storage permissions required to view disk usage',
    loadingStorageInfo: 'Loading storage info...',
    couldNotLoadStorage: 'Could not load storage information',
  },
  analyze: {
    title: 'Analysis',
    largeFiles: 'Space Hogs',
    unusedApps: 'Dormant Apps',
    duplicateFiles: 'Duplicates',
    oldFiles: 'Outdated Files',
    scanNow: 'Scan Now',
    analyzeWithAI: 'Analyze with AI',
    scanning: 'Scanning...',
    startScanText: 'Tap "Scan Now" to analyze your storage',
    noResults: 'No results found',
    scanError: 'Scan Error',
    scanErrorMessage: 'There was an error scanning your storage. Please try again.',
    aiDisabled: 'AI Features Disabled',
    enableAI: 'Enable AI features in settings to use this feature',
    aiRecommendations: 'AI Recommendations',
    aiError: 'AI Analysis Error',
    aiErrorMessage: 'There was an error analyzing your storage. Please try again.',
    featureNotAvailable: 'Feature Not Available',
    featureComingSoon: 'This feature is coming soon in a future update.',
    permissionRequired: 'Permission Required',
    permissionRequiredMessage: 'Storage access permission is needed to analyze your files. Please grant permission in settings.',
    protectedFiles: 'Protected Files',
    protectedFilesMessage: 'Some system files cannot be analyzed for security reasons.',
    noMediaFilesFound: 'No Media Files Found',
    noMediaFilesFoundMessage: 'Unable to find media files to analyze. Please check app permissions and try again.',
    noDuplicatesFound: 'No Duplicates Found',
    noDuplicatesFoundMessage: 'Good news! No duplicate files were found in your media library.',
    noOldFilesFound: 'No Old Files Found',
    noOldFilesFoundMessage: 'Good news! No files older than 3 months were found in your media library.',
    noResults: 'No Results',
    noResultsMessage: 'No files matching the selected criteria were found.',
    smartAnalysisButton: 'Smart Analysis',
    aiAnalysisDisabled: 'AI Analysis Disabled',
    enableAI: 'Enable AI Analysis in Settings to use this feature.',
    goToSettings: 'Go to Settings',
    scanError: 'Scan Error',
    scanErrorMessage: 'An error occurred while scanning. Please try again.',
    scanNow: 'Scan Now',
    scanNowDescription: 'Tap "Scan Now" to analyze your storage',
    noResultsFound: 'No results found',
    optimizeNow: 'Optimize Now'
  },
  clean: {
    title: 'Clean',
    junkFiles: 'Junk Files',
    cacheCleaner: 'Cache Cleaner',
    appData: 'App Data',
    downloadedFiles: 'Downloads',
    selectAll: 'Select All',
    cleanSelected: 'Clean Selected',
  },
  compress: {
    title: 'Compress',
    images: 'Photos',
    videos: 'Videos',
    documents: 'Documents',
    compressionLevel: 'Compression Level',
    light: 'Light',
    medium: 'Medium',
    heavy: 'Heavy',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    noFilesSelected: 'No Files Selected',
    pleaseSelectFiles: 'Please select files to compress',
    compressSelectedFiles: 'Compress Selected Files',
    compressionWarning: 'Are you sure you want to compress {{count}} files? This may reduce quality.',
    compress: 'Compress',
    compressionComplete: 'Compression Complete',
    compressionStats: 'Successfully compressed {{count}} files.\nEstimated space saved: {{space}}.',
    error: 'Error',
    errorMessage: 'Failed to compress files.',
    compressNow: 'Compress Now',
    highCompressionNote: 'Higher compression, lower quality',
    lowCompressionNote: 'Lower compression, higher quality',
    balancedCompressionNote: 'Balanced compression and quality',
    load: 'Load',
    noFilesLoaded: 'No {{category}} loaded. Tap the button above to load {{category}}.',
    selectedCount: '{{selected}} / {{total}} selected'
  },
  settings: {
    title: 'Settings',
    display: 'Display',
    darkMode: 'Dark Mode',
    darkModeDescription: 'Enable dark theme for night usage',
    permissions: 'Permissions',
    storageAccess: 'Storage Access',
    storageAccessDescription: 'Required for analyzing and managing files',
    granted: 'Granted',
    denied: 'Denied',
    request: 'Request',
    securityProtection: 'Security & Protection',
    protectSystemFiles: 'Protect System Files',
    protectSystemFilesDescription: 'Prevent deletion of essential files',
    confirmDeletions: 'Confirm Deletions',
    confirmDeletionsDescription: 'Always ask before deleting files',
    advancedMode: 'Advanced Mode',
    advancedModeDescription: 'For experienced users only',
    languageRegion: 'Language & Region',
    appLanguage: 'App Language',
    smartFeatures: 'Smart Features',
    aiAnalysis: 'AI Analysis',
    aiAnalysisDescription: 'Enable intelligent storage recommendations',
    about: 'About',
    appVersion: 'App Version',
    privacyPolicy: 'Privacy Policy',
    termsOfService: 'Terms of Service',
    securityWarning: 'Security Warning',
    securityWarningMessage: 'Disabling system file protection may allow deleting critical files needed for proper operation. Are you sure you want to proceed?',
    proceed: 'Proceed',
    permissionsRequired: 'Permissions Required',
    permissionsRequiredMessage: 'Storage access permissions are needed for this app to function correctly. Please enable them in your device settings.',
    openSettings: 'Open Settings',
    permissionsGranted: 'Permissions Granted',
    permissionsGrantedMessage: 'Storage access permissions have been granted. All app features should now work correctly.',
  },
  ai: {
    title: 'Smart Assistant',
    suggestion: 'Insights',
    optimize: 'Auto-Optimize',
    help: 'Help',
  },
};

// Hindi translations
const hiTranslations = {
  common: {
    welcome: 'स्टोरेजप्रो में आपका स्वागत है',
    continue: 'जारी रखें',
    cancel: 'रद्द करें',
    save: 'सहेजें',
    delete: 'हटाएं',
    settings: 'सेटिंग्स',
    back: 'वापस',
    grantPermissions: 'अनुमतियाँ दें',
    loading: 'लोड हो रहा है...',
    error: 'त्रुटि',
    retry: 'पुनः प्रयास करें',
    free: 'मुफ्त',
    used: 'उपयोग किया गया',
  },
  tabs: {
    storage: 'स्टोरेज',
    analysis: 'विश्लेषण',
    clean: 'साफ करें',
    compress: 'संपीड़न',
    settings: 'सेटिंग्स',
  },
  languageSelection: {
    chooseLanguage: 'अपनी भाषा चुनें',
    english: 'अंग्रेज़ी',
    hindi: 'हिंदी',
  },
  home: {
    title: 'स्टोरेज',
    storageUsage: 'स्टोरेज उपयोग',
    usedStorage: 'उपयोग की गई जगह',
    freeStorage: 'उपलब्ध जगह',
    quickActions: 'त्वरित कार्रवाई',
    analyzeStorage: 'स्टोरेज का विश्लेषण करें',
    cleanStorage: 'स्टोरेज साफ करें',
    compressFiles: 'फाइलें संपीड़ित करें',
    smartAnalysis: 'स्मार्ट विश्लेषण',
    aiAssistant: 'स्मार्ट सहायक',
    storageTips: 'स्टोरेज टिप्स',
    freeUpSpaceQuickly: 'जल्दी जगह खाली करें',
    freeUpSpaceTip: 'बड़ी फाइलों और डुप्लिकेट्स को खोजने के लिए विश्लेषण टैब देखें जिन्हें आप सुरक्षित रूप से हटा सकते हैं।',
    compressPhotos: 'अपनी तस्वीरों को संपीड़ित करें',
    compressPhotosTip: 'गुणवत्ता खोए बिना अपनी तस्वीरों का आकार कम करने के लिए संपीड़न टैब का उपयोग करें।',
    mediaBreakdown: 'मीडिया विवरण',
    photos: 'तस्वीरें',
    videos: 'वीडियो',
    other: 'अन्य',
    storagePermissionsRequired: 'डिस्क उपयोग देखने के लिए स्टोरेज अनुमतियाँ आवश्यक हैं',
    loadingStorageInfo: 'स्टोरेज जानकारी लोड हो रही है...',
    couldNotLoadStorage: 'स्टोरेज जानकारी लोड नहीं हो सकी',
  },
  analyze: {
    title: 'विश्लेषण',
    largeFiles: 'अधिक जगह लेने वाली फाइलें',
    unusedApps: 'निष्क्रिय ऐप्स',
    duplicateFiles: 'डुप्लिकेट',
    oldFiles: 'पुरानी फाइलें',
    scanNow: 'अभी स्कैन करें',
    analyzeWithAI: 'AI के साथ विश्लेषण',
    scanning: 'स्कैनिंग...',
    startScanText: 'अपने स्टोरेज को विश्लेषण करने के लिए "अभी स्कैन करें" टैप करें',
    noResults: 'कोई परिणाम नहीं मिला',
    scanError: 'स्कैन त्रुटि',
    scanErrorMessage: 'स्टोरेज स्कैन करते समय एक त्रुटि हुई। कृपया पुनः प्रयास करें।',
    aiDisabled: 'AI सुविधाएँ अक्षम',
    enableAI: 'इस सुविधा का उपयोग करने के लिए सेटिंग्स में AI सुविधाएँ सक्षम करें',
    aiRecommendations: 'AI सुझाव',
    aiError: 'AI विश्लेषण त्रुटि',
    aiErrorMessage: 'स्टोरेज विश्लेषण करते समय एक त्रुटि हुई। कृपया पुनः प्रयास करें।',
    featureNotAvailable: 'सुविधा उपलब्ध नहीं',
    featureComingSoon: 'यह सुविधा भविष्य के अपडेट में जल्द ही आने वाली है।',
    permissionRequired: 'अनुमति आवश्यक',
    permissionRequiredMessage: 'आपकी फाइलों का विश्लेषण करने के लिए स्टोरेज एक्सेस अनुमति की आवश्यकता है। कृपया सेटिंग्स में अनुमति दें।',
    protectedFiles: 'सुरक्षित फाइलें',
    protectedFilesMessage: 'सुरक्षा कारणों से कुछ सिस्टम फाइलों का विश्लेषण नहीं किया जा सकता।',
    noMediaFilesFound: 'कोई मीडिया फाइलें नहीं मिलीं',
    noMediaFilesFoundMessage: 'विश्लेषण के लिए मीडिया फाइलें खोजने में असमर्थ। कृपया ऐप अनुमतियाँ जांचें और पुनः प्रयास करें।',
    noDuplicatesFound: 'कोई डुप्लिकेट नहीं मिला',
    noDuplicatesFoundMessage: 'अच्छी खबर! आपकी मीडिया लाइब्रेरी में कोई डुप्लिकेट फाइलें नहीं मिलीं।',
    noOldFilesFound: 'कोई पुरानी फाइलें नहीं मिलीं',
    noOldFilesFoundMessage: 'अच्छी खबर! आपकी मीडिया लाइब्रेरी में 3 महीने से पुरानी कोई फाइलें नहीं मिलीं।',
    noResults: 'कोई परिणाम नहीं',
    noResultsMessage: 'चयनित मानदंडों से मेल खाने वाली कोई फाइलें नहीं मिलीं।',
    smartAnalysisButton: 'स्मार्ट विश्लेषण',
    aiAnalysisDisabled: 'AI विश्लेषण अक्षम',
    enableAI: 'इस सुविधा का उपयोग करने के लिए सेटिंग्स में AI विश्लेषण सक्षम करें।',
    goToSettings: 'सेटिंग्स पर जाएं',
    scanNow: 'अभी स्कैन करें',
    scanNowDescription: 'अपने स्टोरेज का विश्लेषण करने के लिए "अभी स्कैन करें" पर टैप करें',
    noResultsFound: 'कोई परिणाम नहीं मिला',
    optimizeNow: 'अभी ऑप्टिमाइज़ करें'
  },
  clean: {
    title: 'क्लीन',
    junkFiles: 'जंक फाइलें',
    cacheCleaner: 'कैश क्लीनर',
    appData: 'ऐप डेटा',
    downloadedFiles: 'डाउनलोड्स',
    selectAll: 'सभी चुनें',
    cleanSelected: 'चयनित साफ करें',
  },
  compress: {
    title: 'कंप्रेस',
    images: 'फोटो',
    videos: 'वीडियो',
    documents: 'दस्तावेज़',
    compressionLevel: 'ऑप्टिमाइजेशन स्तर',
    light: 'हल्का',
    medium: 'संतुलित',
    heavy: 'अधिकतम',
    selectAll: 'सभी चुनें',
    deselectAll: 'सभी हटाएं',
    noFilesSelected: 'कोई फाइलें नहीं चुनी गई',
    pleaseSelectFiles: 'कृपया फाइलें चुनें',
    compressSelectedFiles: 'चयनित फाइलें संपीड़ित करें',
    compressionWarning: 'क्या आप निश्चित है कि आप इसे संपीड़ित करना चाहते हैं {{count}} फाइलें? इससे गुणवत्ता कम हो सकती है।',
    compress: 'संपीड़ित करें',
    compressionComplete: 'संपीड़न पूर्ण',
    compressionStats: 'सफलतापूर्वक {{count}} फाइलें संपीड़ित करें।\nअनुमानित स्थान बचाया: {{space}}.',
    error: 'त्रुटि',
    errorMessage: 'फाइलें संपीड़ित नहीं कर सकते',
    compressNow: 'अभी संपीड़ित करें',
    highCompressionNote: 'उच्च संपीड़न, कम गुणवत्ता',
    lowCompressionNote: 'कम संपीड़न, उच्च गुणवत्ता',
    balancedCompressionNote: 'संतुलित संपीड़न और गुणवत्ता',
    load: 'लोड',
    noFilesLoaded: 'कोई {{category}} लोड नहीं किया गया। ऊपर दिए गए बटन को टैप करके {{category}} लोड करें।',
    selectedCount: '{{selected}} / {{total}} चयनित'
  },
  settings: {
    title: 'सेटिंग्स',
    display: 'डिस्प्ले',
    darkMode: 'डार्क मोड',
    darkModeDescription: 'रात के उपयोग के लिए डार्क थीम सक्षम करें',
    permissions: 'अनुमतियाँ',
    storageAccess: 'स्टोरेज एक्सेस',
    storageAccessDescription: 'फाइलों का विश्लेषण और प्रबंधन के लिए आवश्यक',
    granted: 'दी गई',
    denied: 'मना किया गया',
    request: 'अनुरोध',
    securityProtection: 'सुरक्षा और सुरक्षा',
    protectSystemFiles: 'सिस्टम फाइलों की सुरक्षा करें',
    protectSystemFilesDescription: 'आवश्यक फाइलों को हटाने से रोकें',
    confirmDeletions: 'हटाने की पुष्टि करें',
    confirmDeletionsDescription: 'फाइलें हटाने से पहले हमेशा पूछें',
    advancedMode: 'एडवांस मोड',
    advancedModeDescription: 'केवल अनुभवी उपयोगकर्ताओं के लिए',
    languageRegion: 'भाषा और क्षेत्र',
    appLanguage: 'ऐप भाषा',
    smartFeatures: 'स्मार्ट सुविधाएँ',
    aiAnalysis: 'AI विश्लेषण',
    aiAnalysisDescription: 'बुद्धिमान स्टोरेज सुझाव सक्षम करें',
    about: 'के बारे में',
    appVersion: 'ऐप संस्करण',
    privacyPolicy: 'गोपनीयता नीति',
    termsOfService: 'सेवा की शर्तें',
    securityWarning: 'सुरक्षा चेतावनी',
    securityWarningMessage: 'सिस्टम फाइल सुरक्षा को अक्षम करने से उचित संचालन के लिए आवश्यक महत्वपूर्ण फाइलों को हटाने की अनुमति मिल सकती है। क्या आप वाकई आगे बढ़ना चाहते हैं?',
    proceed: 'आगे बढ़ें',
    permissionsRequired: 'अनुमतियाँ आवश्यक',
    permissionsRequiredMessage: 'इस ऐप के सही तरीके से काम करने के लिए स्टोरेज एक्सेस अनुमतियाँ आवश्यक हैं। कृपया उन्हें अपनी डिवाइस सेटिंग्स में सक्षम करें।',
    openSettings: 'सेटिंग्स खोलें',
    permissionsGranted: 'अनुमतियाँ दी गईं',
    permissionsGrantedMessage: 'स्टोरेज एक्सेस अनुमतियाँ दी गई हैं। अब सभी ऐप सुविधाएँ सही तरीके से काम करनी चाहिए।',
  },
  ai: {
    title: 'स्मार्ट सहायक',
    suggestion: 'अंतर्दृष्टि',
    optimize: 'स्वचालित-अनुकूलन',
    help: 'मदद',
  },
};

// Initialize i18n
i18n.use(initReactI18next).init({
  resources: {
    en: {
      translation: enTranslations,
    },
    hi: {
      translation: hiTranslations,
    },
  },
  lng: 'en',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;