import * as FileSystem from 'expo-file-system';
import { FileInfo, getFileType } from './storageUtils';

export interface OrganizationRule {
  id: string;
  name: string;
  description: string;
  condition: (file: FileInfo) => boolean;
  action: 'move' | 'copy' | 'compress' | 'delete';
  destination?: string;
  enabled: boolean;
}

export interface OrganizationResult {
  success: number;
  failed: number;
  totalSize: number;
  details: {
    file: FileInfo;
    action: string;
    result: 'success' | 'failed';
    error?: string;
  }[];
}

/**
 * Smart file organizer with predefined and custom rules
 */
export class SmartFileOrganizer {
  private rules: OrganizationRule[] = [];

  constructor() {
    this.initializeDefaultRules();
  }

  /**
   * Initialize default organization rules
   */
  private initializeDefaultRules() {
    this.rules = [
      {
        id: 'organize-images',
        name: 'Organize Images by Date',
        description: 'Move images to folders organized by year and month',
        condition: (file) => file.type === 'image',
        action: 'move',
        enabled: true,
      },
      {
        id: 'organize-videos',
        name: 'Organize Videos by Date',
        description: 'Move videos to folders organized by year and month',
        condition: (file) => file.type === 'video',
        action: 'move',
        enabled: true,
      },
      {
        id: 'organize-documents',
        name: 'Organize Documents by Type',
        description: 'Move documents to folders by file type',
        condition: (file) => ['document', 'spreadsheet', 'presentation'].includes(file.type || ''),
        action: 'move',
        enabled: true,
      },
      {
        id: 'compress-large-images',
        name: 'Compress Large Images',
        description: 'Automatically compress images larger than 5MB',
        condition: (file) => file.type === 'image' && file.size > 5 * 1024 * 1024,
        action: 'compress',
        enabled: false, // Disabled by default for safety
      },
      {
        id: 'clean-old-downloads',
        name: 'Clean Old Downloads',
        description: 'Delete downloads older than 30 days',
        condition: (file) => {
          const age = Date.now() - (file.modificationTime || 0);
          const thirtyDays = 30 * 24 * 60 * 60 * 1000;
          return file.path.includes('Download') && age > thirtyDays;
        },
        action: 'delete',
        enabled: false, // Disabled by default for safety
      },
    ];
  }

  /**
   * Get all organization rules
   */
  getRules(): OrganizationRule[] {
    return this.rules;
  }

  /**
   * Add a custom organization rule
   */
  addRule(rule: Omit<OrganizationRule, 'id'>): void {
    const newRule: OrganizationRule = {
      ...rule,
      id: `custom-${Date.now()}`,
    };
    this.rules.push(newRule);
  }

  /**
   * Update an existing rule
   */
  updateRule(id: string, updates: Partial<OrganizationRule>): void {
    const index = this.rules.findIndex(rule => rule.id === id);
    if (index !== -1) {
      this.rules[index] = { ...this.rules[index], ...updates };
    }
  }

  /**
   * Remove a rule
   */
  removeRule(id: string): void {
    this.rules = this.rules.filter(rule => rule.id !== id);
  }

  /**
   * Organize files based on enabled rules
   */
  async organizeFiles(files: FileInfo[]): Promise<OrganizationResult> {
    const result: OrganizationResult = {
      success: 0,
      failed: 0,
      totalSize: 0,
      details: [],
    };

    const enabledRules = this.rules.filter(rule => rule.enabled);

    for (const file of files) {
      for (const rule of enabledRules) {
        if (rule.condition(file)) {
          try {
            const actionResult = await this.executeAction(file, rule);
            if (actionResult.success) {
              result.success++;
              result.totalSize += file.size;
              result.details.push({
                file,
                action: rule.action,
                result: 'success',
              });
            } else {
              result.failed++;
              result.details.push({
                file,
                action: rule.action,
                result: 'failed',
                error: actionResult.error,
              });
            }
          } catch (error) {
            result.failed++;
            result.details.push({
              file,
              action: rule.action,
              result: 'failed',
              error: error instanceof Error ? error.message : 'Unknown error',
            });
          }
          break; // Only apply the first matching rule
        }
      }
    }

    return result;
  }

  /**
   * Execute the action specified by a rule
   */
  private async executeAction(
    file: FileInfo,
    rule: OrganizationRule
  ): Promise<{ success: boolean; error?: string }> {
    try {
      switch (rule.action) {
        case 'move':
          return await this.moveFile(file, rule);
        case 'copy':
          return await this.copyFile(file, rule);
        case 'compress':
          return await this.compressFile(file);
        case 'delete':
          return await this.deleteFile(file);
        default:
          return { success: false, error: 'Unknown action' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Move file to organized location
   */
  private async moveFile(
    file: FileInfo,
    rule: OrganizationRule
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const destination = rule.destination || this.generateDestinationPath(file, rule);
      
      // Ensure destination directory exists
      await this.ensureDirectoryExists(destination);
      
      const newPath = `${destination}/${file.name}`;
      
      // Check if file already exists at destination
      const existsInfo = await FileSystem.getInfoAsync(newPath);
      if (existsInfo.exists) {
        return { success: false, error: 'File already exists at destination' };
      }
      
      await FileSystem.moveAsync({
        from: file.uri,
        to: newPath,
      });
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Move failed',
      };
    }
  }

  /**
   * Copy file to organized location
   */
  private async copyFile(
    file: FileInfo,
    rule: OrganizationRule
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const destination = rule.destination || this.generateDestinationPath(file, rule);
      
      await this.ensureDirectoryExists(destination);
      
      const newPath = `${destination}/${file.name}`;
      
      await FileSystem.copyAsync({
        from: file.uri,
        to: newPath,
      });
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Copy failed',
      };
    }
  }

  /**
   * Compress file (placeholder - would need actual compression implementation)
   */
  private async compressFile(file: FileInfo): Promise<{ success: boolean; error?: string }> {
    // This would integrate with the existing compression utilities
    // For now, return success as placeholder
    return { success: true };
  }

  /**
   * Delete file
   */
  private async deleteFile(file: FileInfo): Promise<{ success: boolean; error?: string }> {
    try {
      await FileSystem.deleteAsync(file.uri);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete failed',
      };
    }
  }

  /**
   * Generate destination path based on file and rule
   */
  private generateDestinationPath(file: FileInfo, rule: OrganizationRule): string {
    const baseDir = FileSystem.documentDirectory || '';
    
    if (rule.id === 'organize-images' || rule.id === 'organize-videos') {
      const date = new Date(file.modificationTime || Date.now());
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${baseDir}Organized/${file.type}s/${year}/${month}`;
    }
    
    if (rule.id === 'organize-documents') {
      return `${baseDir}Organized/Documents/${file.type}`;
    }
    
    return `${baseDir}Organized/Other`;
  }

  /**
   * Ensure directory exists, create if it doesn't
   */
  private async ensureDirectoryExists(path: string): Promise<void> {
    const info = await FileSystem.getInfoAsync(path);
    if (!info.exists) {
      await FileSystem.makeDirectoryAsync(path, { intermediates: true });
    }
  }

  /**
   * Preview what would happen with current rules (dry run)
   */
  async previewOrganization(files: FileInfo[]): Promise<{
    affectedFiles: { file: FileInfo; rule: OrganizationRule; action: string }[];
    estimatedSavings: number;
  }> {
    const affectedFiles: { file: FileInfo; rule: OrganizationRule; action: string }[] = [];
    let estimatedSavings = 0;

    const enabledRules = this.rules.filter(rule => rule.enabled);

    for (const file of files) {
      for (const rule of enabledRules) {
        if (rule.condition(file)) {
          affectedFiles.push({
            file,
            rule,
            action: rule.action,
          });
          
          if (rule.action === 'compress') {
            estimatedSavings += file.size * 0.4; // Estimate 40% compression
          } else if (rule.action === 'delete') {
            estimatedSavings += file.size;
          }
          
          break; // Only apply the first matching rule
        }
      }
    }

    return { affectedFiles, estimatedSavings };
  }
}
