import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { FileInfo, formatBytes } from './storageUtils';

export interface CloudProvider {
  id: string;
  name: string;
  icon: string;
  maxFileSize: number;
  freeStorage: number;
  isConnected: boolean;
  lastSync?: number;
}

export interface BackupJob {
  id: string;
  files: FileInfo[];
  provider: string;
  status: 'pending' | 'uploading' | 'completed' | 'failed';
  progress: number;
  totalSize: number;
  uploadedSize: number;
  startTime: number;
  endTime?: number;
  error?: string;
}

export interface BackupSettings {
  autoBackupEnabled: boolean;
  backupOnWiFiOnly: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  includeVideos: boolean;
  includePhotos: boolean;
  includeDocuments: boolean;
  maxFileSize: number;
  compressionEnabled: boolean;
}

export class CloudBackupManager {
  private static instance: CloudBackupManager;
  private providers: CloudProvider[] = [];
  private activeJobs: BackupJob[] = [];
  private settings: BackupSettings;

  private constructor() {
    this.initializeProviders();
    this.loadSettings();
  }

  static getInstance(): CloudBackupManager {
    if (!CloudBackupManager.instance) {
      CloudBackupManager.instance = new CloudBackupManager();
    }
    return CloudBackupManager.instance;
  }

  /**
   * Initialize cloud providers
   */
  private initializeProviders(): void {
    this.providers = [
      {
        id: 'google-drive',
        name: 'Google Drive',
        icon: 'cloud.fill',
        maxFileSize: 5 * 1024 * 1024 * 1024, // 5GB
        freeStorage: 15 * 1024 * 1024 * 1024, // 15GB
        isConnected: false,
      },
      {
        id: 'dropbox',
        name: 'Dropbox',
        icon: 'cloud.fill',
        maxFileSize: 350 * 1024 * 1024, // 350MB for free accounts
        freeStorage: 2 * 1024 * 1024 * 1024, // 2GB
        isConnected: false,
      },
      {
        id: 'onedrive',
        name: 'OneDrive',
        icon: 'cloud.fill',
        maxFileSize: 250 * 1024 * 1024, // 250MB
        freeStorage: 5 * 1024 * 1024 * 1024, // 5GB
        isConnected: false,
      },
      {
        id: 'icloud',
        name: 'iCloud',
        icon: 'cloud.fill',
        maxFileSize: 50 * 1024 * 1024 * 1024, // 50GB
        freeStorage: 5 * 1024 * 1024 * 1024, // 5GB
        isConnected: false,
      },
    ];
  }

  /**
   * Load backup settings
   */
  private async loadSettings(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('backup_settings');
      this.settings = stored ? JSON.parse(stored) : {
        autoBackupEnabled: false,
        backupOnWiFiOnly: true,
        backupFrequency: 'weekly',
        includeVideos: true,
        includePhotos: true,
        includeDocuments: true,
        maxFileSize: 100 * 1024 * 1024, // 100MB
        compressionEnabled: true,
      };
    } catch (error) {
      console.error('Error loading backup settings:', error);
      this.settings = {
        autoBackupEnabled: false,
        backupOnWiFiOnly: true,
        backupFrequency: 'weekly',
        includeVideos: true,
        includePhotos: true,
        includeDocuments: true,
        maxFileSize: 100 * 1024 * 1024,
        compressionEnabled: true,
      };
    }
  }

  /**
   * Save backup settings
   */
  async saveSettings(settings: Partial<BackupSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    try {
      await AsyncStorage.setItem('backup_settings', JSON.stringify(this.settings));
    } catch (error) {
      console.error('Error saving backup settings:', error);
    }
  }

  /**
   * Get available cloud providers
   */
  getProviders(): CloudProvider[] {
    return this.providers;
  }

  /**
   * Connect to a cloud provider
   */
  async connectProvider(providerId: string): Promise<boolean> {
    const provider = this.providers.find(p => p.id === providerId);
    if (!provider) return false;

    try {
      // In a real implementation, this would handle OAuth flow
      // For now, simulate connection
      provider.isConnected = true;
      provider.lastSync = Date.now();
      
      await this.saveProviderState();
      return true;
    } catch (error) {
      console.error(`Error connecting to ${providerId}:`, error);
      return false;
    }
  }

  /**
   * Disconnect from a cloud provider
   */
  async disconnectProvider(providerId: string): Promise<void> {
    const provider = this.providers.find(p => p.id === providerId);
    if (provider) {
      provider.isConnected = false;
      provider.lastSync = undefined;
      await this.saveProviderState();
    }
  }

  /**
   * Start backup job
   */
  async startBackup(files: FileInfo[], providerId: string): Promise<string> {
    const provider = this.providers.find(p => p.id === providerId);
    if (!provider || !provider.isConnected) {
      throw new Error('Provider not connected');
    }

    // Filter files based on settings
    const filteredFiles = this.filterFilesForBackup(files);
    const totalSize = filteredFiles.reduce((sum, f) => sum + f.size, 0);

    const job: BackupJob = {
      id: `backup_${Date.now()}`,
      files: filteredFiles,
      provider: providerId,
      status: 'pending',
      progress: 0,
      totalSize,
      uploadedSize: 0,
      startTime: Date.now(),
    };

    this.activeJobs.push(job);
    
    // Start the backup process
    this.processBackupJob(job);
    
    return job.id;
  }

  /**
   * Process backup job
   */
  private async processBackupJob(job: BackupJob): Promise<void> {
    try {
      job.status = 'uploading';
      
      for (let i = 0; i < job.files.length; i++) {
        const file = job.files[i];
        
        // Simulate upload process
        await this.uploadFile(file, job.provider);
        
        job.uploadedSize += file.size;
        job.progress = (job.uploadedSize / job.totalSize) * 100;
        
        // Notify progress (in real app, would use event emitters)
        console.log(`Backup progress: ${job.progress.toFixed(1)}%`);
      }
      
      job.status = 'completed';
      job.endTime = Date.now();
      
    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.endTime = Date.now();
    }
  }

  /**
   * Upload file to cloud provider
   */
  private async uploadFile(file: FileInfo, providerId: string): Promise<void> {
    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In real implementation, this would:
    // 1. Compress file if enabled
    // 2. Upload to specific cloud provider API
    // 3. Handle authentication and errors
    // 4. Update sync status
    
    console.log(`Uploaded ${file.name} to ${providerId}`);
  }

  /**
   * Filter files based on backup settings
   */
  private filterFilesForBackup(files: FileInfo[]): FileInfo[] {
    return files.filter(file => {
      // Check file type
      if (!this.settings.includePhotos && file.type === 'image') return false;
      if (!this.settings.includeVideos && file.type === 'video') return false;
      if (!this.settings.includeDocuments && ['document', 'spreadsheet', 'presentation'].includes(file.type || '')) return false;
      
      // Check file size
      if (file.size > this.settings.maxFileSize) return false;
      
      return true;
    });
  }

  /**
   * Get backup jobs
   */
  getBackupJobs(): BackupJob[] {
    return this.activeJobs;
  }

  /**
   * Get backup job by ID
   */
  getBackupJob(jobId: string): BackupJob | undefined {
    return this.activeJobs.find(job => job.id === jobId);
  }

  /**
   * Cancel backup job
   */
  cancelBackupJob(jobId: string): boolean {
    const job = this.activeJobs.find(j => j.id === jobId);
    if (job && job.status === 'uploading') {
      job.status = 'failed';
      job.error = 'Cancelled by user';
      job.endTime = Date.now();
      return true;
    }
    return false;
  }

  /**
   * Get backup statistics
   */
  getBackupStats(): {
    totalBackups: number;
    successfulBackups: number;
    failedBackups: number;
    totalDataBackedUp: number;
    lastBackupTime?: number;
  } {
    const completedJobs = this.activeJobs.filter(j => j.status === 'completed');
    const failedJobs = this.activeJobs.filter(j => j.status === 'failed');
    const totalDataBackedUp = completedJobs.reduce((sum, j) => sum + j.totalSize, 0);
    const lastBackupTime = completedJobs.length > 0 ? 
      Math.max(...completedJobs.map(j => j.endTime || 0)) : undefined;

    return {
      totalBackups: this.activeJobs.length,
      successfulBackups: completedJobs.length,
      failedBackups: failedJobs.length,
      totalDataBackedUp,
      lastBackupTime,
    };
  }

  /**
   * Check if auto backup should run
   */
  shouldRunAutoBackup(): boolean {
    if (!this.settings.autoBackupEnabled) return false;
    
    const stats = this.getBackupStats();
    if (!stats.lastBackupTime) return true;
    
    const timeSinceLastBackup = Date.now() - stats.lastBackupTime;
    const intervals = {
      daily: 24 * 60 * 60 * 1000,
      weekly: 7 * 24 * 60 * 60 * 1000,
      monthly: 30 * 24 * 60 * 60 * 1000,
    };
    
    return timeSinceLastBackup > intervals[this.settings.backupFrequency];
  }

  /**
   * Get backup settings
   */
  getSettings(): BackupSettings {
    return this.settings;
  }

  /**
   * Save provider state
   */
  private async saveProviderState(): Promise<void> {
    try {
      await AsyncStorage.setItem('cloud_providers', JSON.stringify(this.providers));
    } catch (error) {
      console.error('Error saving provider state:', error);
    }
  }

  /**
   * Restore files from backup
   */
  async restoreFromBackup(providerId: string, files: string[]): Promise<boolean> {
    const provider = this.providers.find(p => p.id === providerId);
    if (!provider || !provider.isConnected) {
      throw new Error('Provider not connected');
    }

    try {
      // In real implementation, this would download files from cloud
      console.log(`Restoring ${files.length} files from ${providerId}`);
      return true;
    } catch (error) {
      console.error('Error restoring from backup:', error);
      return false;
    }
  }
}
