/**
 * Generate placeholder SVG icons for the app
 * 
 * This script creates placeholder SVG icons for all the icons listed in the icon-manifest.ts file.
 * It's meant to be used during development to quickly generate all the needed icon files.
 * 
 * Usage: node scripts/generate-placeholder-icons.js
 */

const fs = require('fs');
const path = require('path');

// List of all the icons needed by the app with their modern filenames
const REQUIRED_ICONS = [
  'home',
  'analytics',
  'clean',
  'optimize',
  'settings',
  'arrow-right',
  'back',
  'storage',
  'duplicate',
  'calendar',
  'app',
  'ai',
  'refresh',
  'download',
  'photo',
  'video',
  'document',
  'text-document',
  'music',
  'spreadsheet',
  'presentation',
  'folder',
];

// Path to the icons directory
const ICONS_DIR = path.join(__dirname, '..', 'assets', 'icons');

// Simple SVG template for placeholder icons
const generatePlaceholderSVG = (name) => {
  // Create a friendly label from the icon name
  const label = name.charAt(0).toUpperCase();
  
  return `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
  <path d="M0 0h24v24H0z" fill="none"/>
  <circle cx="12" cy="12" r="10" fill="currentColor" />
  <text x="12" y="16" text-anchor="middle" font-size="14" fill="#fff">${label}</text>
</svg>`;
};

// Create the icons directory if it doesn't exist
if (!fs.existsSync(ICONS_DIR)) {
  fs.mkdirSync(ICONS_DIR, { recursive: true });
  console.log(`Created icons directory: ${ICONS_DIR}`);
}

// Generate placeholder SVGs for all required icons
REQUIRED_ICONS.forEach((iconName) => {
  const filePath = path.join(ICONS_DIR, `${iconName}.svg`);
  
  // Skip existing files
  if (fs.existsSync(filePath)) {
    console.log(`Skipping ${iconName}.svg (already exists)`);
    return;
  }
  
  // Write the SVG file
  fs.writeFileSync(filePath, generatePlaceholderSVG(iconName));
  console.log(`Created placeholder icon: ${iconName}.svg`);
});

console.log('\nDone! Generated placeholder icons.');
console.log('Replace these with actual SVG icons for production use.');
console.log('See assets/icons/README.md for more information.');